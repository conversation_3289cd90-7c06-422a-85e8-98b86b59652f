<?php
/**
 * หน้าจองคิวสำหรับผู้ใช้ทั่วไป (ไม่ต้องล็อกอิน)
 */

require_once __DIR__ . '/../includes/functions.php';

$error_message = '';
$success_message = '';

// ดึงข้อมูลบริการ
$services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");

// ดึงข้อมูลช่างตัดผม
$barbers = fetchAll("SELECT * FROM barbers WHERE is_active = 1 ORDER BY name");

// ดึงข้อมูลโปรโมชั่น
$promotions = fetchAll("SELECT * FROM promotions WHERE is_active = 1 AND start_date <= CURDATE() AND end_date >= CURDATE() ORDER BY title");

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_name = trim($_POST['customer_name'] ?? '');
    $customer_phone = trim($_POST['customer_phone'] ?? '');
    $customer_email = trim($_POST['customer_email'] ?? '');
    $service_id = $_POST['service_id'] ?? '';
    $barber_id = $_POST['barber_id'] ?? '';
    $booking_date = $_POST['booking_date'] ?? '';
    $booking_time = $_POST['booking_time'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($customer_name) || empty($customer_phone) || empty($service_id) || empty($barber_id) || empty($booking_date) || empty($booking_time)) {
        $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
    }
    // ตรวจสอบรูปแบบอีเมล
    elseif (!empty($customer_email) && !isValidEmail($customer_email)) {
        $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
    }
    // ตรวจสอบวันที่จอง
    elseif (strtotime($booking_date) < strtotime(date('Y-m-d'))) {
        $error_message = 'ไม่สามารถจองย้อนหลังได้';
    }
    else {
        // ตรวจสอบว่าเวลานี้ว่างหรือไม่
        $existing_booking = fetchOne("
            SELECT id FROM bookings 
            WHERE barber_id = ? AND booking_date = ? AND booking_time = ? 
            AND status NOT IN ('cancelled', 'completed')
        ", [$barber_id, $booking_date, $booking_time]);
        
        if ($existing_booking) {
            $error_message = 'เวลานี้มีการจองแล้ว กรุณาเลือกเวลาอื่น';
        } else {
            // ดึงข้อมูลบริการเพื่อคำนวณราคา
            $service = fetchOne("SELECT * FROM services WHERE id = ?", [$service_id]);
            
            if ($service) {
                $total_price = $service['price'];
                
                // สร้างการจอง
                $sql = "INSERT INTO bookings (customer_name, customer_phone, customer_email, service_id, barber_id, booking_date, booking_time, notes, total_price, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
                
                $result = executeQuery($sql, [
                    $customer_name,
                    $customer_phone,
                    $customer_email,
                    $service_id,
                    $barber_id,
                    $booking_date,
                    $booking_time,
                    $notes,
                    $total_price
                ]);
                
                if ($result) {
                    $booking_id = getLastInsertId();
                    $success_message = "จองคิวสำเร็จ! หมายเลขการจอง: #$booking_id";
                    
                    // ล้างข้อมูลฟอร์ม
                    $_POST = [];
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง';
                }
            } else {
                $error_message = 'ไม่พบข้อมูลบริการที่เลือก';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จองคิว - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">ผลงาน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="booking.php">จองคิว</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว (สมาชิก)</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2><i class="fas fa-calendar-plus text-primary"></i> จองคิวตัดผม</h2>
                <p class="text-muted">จองคิวล่วงหน้าเพื่อความสะดวกของคุณ</p>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Booking Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> ฟอร์มจองคิว</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                            
                            <!-- ข้อมูลลูกค้า -->
                            <h6 class="text-primary mb-3"><i class="fas fa-user"></i> ข้อมูลลูกค้า</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="customerName" name="customer_name" 
                                               value="<?= sanitize($_POST['customer_name'] ?? '') ?>" required>
                                        <label for="customerName">ชื่อ-นามสกุล *</label>
                                        <div class="invalid-feedback">กรุณากรอกชื่อ-นามสกุล</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="tel" class="form-control" id="customerPhone" name="customer_phone" 
                                               value="<?= sanitize($_POST['customer_phone'] ?? '') ?>" required>
                                        <label for="customerPhone">เบอร์โทรศัพท์ *</label>
                                        <div class="invalid-feedback">กรุณากรอกเบอร์โทรศัพท์</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="customerEmail" name="customer_email" 
                                       value="<?= sanitize($_POST['customer_email'] ?? '') ?>">
                                <label for="customerEmail">อีเมล (ไม่บังคับ)</label>
                            </div>

                            <!-- ข้อมูลการจอง -->
                            <h6 class="text-primary mb-3"><i class="fas fa-cut"></i> ข้อมูลการจอง</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" id="serviceId" name="service_id" required>
                                            <option value="">เลือกบริการ</option>
                                            <?php foreach ($services as $service): ?>
                                                <option value="<?= $service['id'] ?>" 
                                                        data-price="<?= $service['price'] ?>"
                                                        data-duration="<?= $service['duration'] ?>"
                                                        <?= ($_POST['service_id'] ?? '') == $service['id'] ? 'selected' : '' ?>>
                                                    <?= sanitize($service['name']) ?> - <?= number_format($service['price'], 2) ?> บาท
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="serviceId">เลือกบริการ *</label>
                                        <div class="invalid-feedback">กรุณาเลือกบริการ</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" id="barberId" name="barber_id" required>
                                            <option value="">เลือกช่างตัดผม</option>
                                            <?php foreach ($barbers as $barber): ?>
                                                <option value="<?= $barber['id'] ?>"
                                                        <?= ($_POST['barber_id'] ?? '') == $barber['id'] ? 'selected' : '' ?>>
                                                    <?= sanitize($barber['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="barberId">เลือกช่างตัดผม *</label>
                                        <div class="invalid-feedback">กรุณาเลือกช่างตัดผม</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="date" class="form-control" id="bookingDate" name="booking_date" 
                                               min="<?= date('Y-m-d') ?>" 
                                               value="<?= sanitize($_POST['booking_date'] ?? '') ?>" required>
                                        <label for="bookingDate">วันที่จอง *</label>
                                        <div class="invalid-feedback">กรุณาเลือกวันที่จอง</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" id="bookingTime" name="booking_time" required>
                                            <option value="">เลือกเวลา</option>
                                            <?php
                                            $times = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', 
                                                     '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', 
                                                     '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'];
                                            foreach ($times as $time):
                                            ?>
                                                <option value="<?= $time ?>"
                                                        <?= ($_POST['booking_time'] ?? '') == $time ? 'selected' : '' ?>>
                                                    <?= $time ?> น.
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="bookingTime">เวลาจอง *</label>
                                        <div class="invalid-feedback">กรุณาเลือกเวลาจอง</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="notes" name="notes" style="height: 100px"><?= sanitize($_POST['notes'] ?? '') ?></textarea>
                                <label for="notes">หมายเหตุเพิ่มเติม</label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-check"></i> จองคิว
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="row mt-5">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                        <h5>เวลาทำการ</h5>
                        <p>จันทร์ - อาทิตย์<br>09:00 - 19:00 น.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                        <h5>ติดต่อสอบถาม</h5>
                        <p>โทร: 02-123-4567<br>Line: @barbershop</p>
                        <div class="d-flex gap-2 justify-content-center flex-wrap">
                            <a href="https://www.facebook.com/kittin.singwee.3" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-facebook-f"></i> Facebook
                            </a>
                            <a href="https://www.instagram.com/kittin__singwee/" target="_blank" class="btn btn-outline-danger btn-sm">
                                <i class="fab fa-instagram"></i> Instagram
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                        <h5>ที่อยู่</h5>
                        <p>123 ถนนสุขุมวิท<br>กรุงเทพฯ 10110</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Auto-hide alerts
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    alert.classList.add('fade');
                    setTimeout(() => alert.remove(), 150);
                }
            });
        }, 5000);
    </script>
</body>
</html>
