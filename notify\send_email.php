<?php
/**
 * ไฟล์สำหรับส่งอีเมลแจ้งเตือนขั้นสูง
 * Advanced Email Notification System
 */

require_once __DIR__ . '/../includes/functions.php';

// สำหรับใช้ PHPMailer (ต้องติดตั้งผ่าน Composer)
// require_once __DIR__ . '/../vendor/autoload.php';
// use P<PERSON>Mailer\PHPMailer\PHPMailer;
// use PHPMailer\PHPMailer\SMTP;
// use PHPMailer\PHPMailer\Exception;

/**
 * การตั้งค่าอีเมลขั้นสูง
 */
class EmailNotification {
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $from_email;
    private $from_name;

    public function __construct() {
        $this->smtp_host = 'smtp.gmail.com';
        $this->smtp_port = 587;
        $this->smtp_username = '<EMAIL>';
        $this->smtp_password = 'your-app-password';
        $this->from_email = '<EMAIL>';
        $this->from_name = 'ร้านตัดผม Barber Shop';
    }

    /**
     * ส่งอีเมลด้วย PHPMailer
     */
    public function sendWithPHPMailer($to_email, $to_name, $subject, $html_body, $text_body = '') {
        /*
        try {
            $mail = new PHPMailer(true);

            // Server settings
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_username;
            $mail->Password = $this->smtp_password;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtp_port;
            $mail->CharSet = 'UTF-8';

            // Recipients
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($to_email, $to_name);
            $mail->addReplyTo($this->from_email, $this->from_name);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $html_body;
            $mail->AltBody = $text_body ?: strip_tags($html_body);

            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("PHPMailer Error: {$mail->ErrorInfo}");
            return false;
        }
        */

        // ใช้ mail() function แทนถ้าไม่มี PHPMailer
        return $this->sendWithMailFunction($to_email, $to_name, $subject, $html_body);
    }

    /**
     * ส่งอีเมลด้วย mail() function
     */
    public function sendWithMailFunction($to_email, $to_name, $subject, $html_body) {
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-type: text/html; charset=UTF-8\r\n";
        $headers .= "From: {$this->from_name} <{$this->from_email}>\r\n";
        $headers .= "Reply-To: {$this->from_email}\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

        return mail($to_email, $subject, $html_body, $headers);
    }

    /**
     * ส่งอีเมลยืนยันการจองแบบขั้นสูง
     */
    public function sendAdvancedBookingConfirmation($booking_id) {
        $booking = $this->getBookingDetails($booking_id);
        if (!$booking) return false;

        $subject = "ยืนยันการจองคิว #{$booking['id']} - ร้านตัดผม Barber Shop";
        $html_body = $this->createAdvancedBookingTemplate($booking);

        return $this->sendWithPHPMailer(
            $booking['customer_email'],
            $booking['customer_name'],
            $subject,
            $html_body
        );
    }

    /**
     * ส่งอีเมลแจ้งเตือนก่อนนัดหมาย
     */
    public function sendAppointmentReminder($booking_id) {
        $booking = $this->getBookingDetails($booking_id);
        if (!$booking) return false;

        $subject = "แจ้งเตือน: การนัดหมายของคุณพรุ่งนี้ - ร้านตัดผม Barber Shop";
        $html_body = $this->createReminderTemplate($booking);

        return $this->sendWithPHPMailer(
            $booking['customer_email'],
            $booking['customer_name'],
            $subject,
            $html_body
        );
    }

    /**
     * ส่งอีเมลขอบคุณหลังใช้บริการ
     */
    public function sendThankYouEmail($booking_id) {
        $booking = $this->getBookingDetails($booking_id);
        if (!$booking) return false;

        $subject = "ขอบคุณที่ใช้บริการ - ร้านตัดผม Barber Shop";
        $html_body = $this->createThankYouTemplate($booking);

        return $this->sendWithPHPMailer(
            $booking['customer_email'],
            $booking['customer_name'],
            $subject,
            $html_body
        );
    }

    /**
     * ดึงข้อมูลการจองแบบละเอียด
     */
    private function getBookingDetails($booking_id) {
        $sql = "SELECT b.*, u.full_name as customer_name, u.email as customer_email, u.phone as customer_phone,
                       s.name as service_name, s.description as service_description, s.duration, s.price as service_price,
                       br.name as barber_name, br.phone as barber_phone, br.speciality
                FROM bookings b
                JOIN users u ON b.user_id = u.id
                JOIN services s ON b.service_id = s.id
                JOIN barbers br ON b.barber_id = br.id
                WHERE b.id = ?";

        return fetchOne($sql, [$booking_id]);
    }

    /**
     * สร้างเทมเพลตอีเมลขั้นสูง
     */
    private function createAdvancedBookingTemplate($booking) {
        $booking_date = formatDateThai($booking['booking_date']);
        $booking_time = formatTime12Hour($booking['booking_time']);
        $end_time = date('H:i', strtotime($booking['booking_time']) + ($booking['duration'] * 60));

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>ยืนยันการจองคิว</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; background: #ffffff; }
                .header { background: linear-gradient(135deg, #2c3e50, #3498db); color: white; padding: 30px; text-align: center; }
                .content { padding: 30px; }
                .booking-card { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; border-left: 5px solid #3498db; }
                .info-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 10px 0; border-bottom: 1px solid #eee; }
                .label { font-weight: bold; color: #2c3e50; }
                .value { color: #34495e; }
                .highlight { background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
                .footer { background: #2c3e50; color: white; padding: 20px; text-align: center; }
                .btn { display: inline-block; padding: 12px 25px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
                .social { margin: 20px 0; }
                .social a { margin: 0 10px; color: #3498db; text-decoration: none; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>✂️ ยืนยันการจองคิว</h1>
                    <p>ร้านตัดผม Barber Shop</p>
                </div>

                <div class='content'>
                    <h2>สวัสดีคุณ {$booking['customer_name']}</h2>
                    <p>ขอบคุณที่เลือกใช้บริการของเรา การจองคิวของท่านได้รับการยืนยันแล้ว</p>

                    <div class='booking-card'>
                        <h3>📋 รายละเอียดการจอง</h3>
                        <div class='info-row'>
                            <span class='label'>หมายเลขการจอง:</span>
                            <span class='value'>#{$booking['id']}</span>
                        </div>
                        <div class='info-row'>
                            <span class='label'>บริการ:</span>
                            <span class='value'>{$booking['service_name']}</span>
                        </div>
                        <div class='info-row'>
                            <span class='label'>ช่างตัดผม:</span>
                            <span class='value'>{$booking['barber_name']}</span>
                        </div>
                        <div class='info-row'>
                            <span class='label'>วันที่:</span>
                            <span class='value'>{$booking_date}</span>
                        </div>
                        <div class='info-row'>
                            <span class='label'>เวลา:</span>
                            <span class='value'>{$booking_time} - {$end_time} ({$booking['duration']} นาที)</span>
                        </div>
                        <div class='info-row'>
                            <span class='label'>ราคา:</span>
                            <span class='value'>" . number_format($booking['total_price']) . " บาท</span>
                        </div>
                    </div>

                    <div class='highlight'>
                        <h4>📍 ข้อมูลสำคัญ</h4>
                        <ul>
                            <li>กรุณามาถึงก่อนเวลานัดหมาย 10 นาที</li>
                            <li>หากต้องการยกเลิก กรุณาแจ้งล่วงหน้าอย่างน้อย 2 ชั่วโมง</li>
                            <li>สามารถจอดรถได้ที่หน้าร้าน</li>
                            <li>รับชำระเงินสด, โอนเงิน, บัตรเครดิต และ QR Code</li>
                        </ul>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='#' class='btn'>ดูรายละเอียดการจอง</a>
                        <a href='#' class='btn' style='background: #e74c3c;'>ยกเลิกการจอง</a>
                    </div>
                </div>

                <div class='footer'>
                    <h4>ติดต่อเรา</h4>
                    <p>📞 081-234-5678 | 📧 <EMAIL></p>
                    <p>📍 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                    <p>🕒 จันทร์-เสาร์ 09:00-20:00 | อาทิตย์ 10:00-18:00</p>

                    <div class='social'>
                        <a href='#'>Facebook</a> |
                        <a href='#'>LINE</a> |
                        <a href='#'>Instagram</a>
                    </div>

                    <p style='font-size: 12px; margin-top: 20px;'>
                        © 2024 Barber Shop. All rights reserved.
                    </p>
                </div>
            </div>
        </body>
        </html>";
    }
}