<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเข้าถึงรูปภาพโดยตรง</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-image { max-width: 200px; max-height: 200px; border: 2px solid #ddd; margin: 10px; border-radius: 8px; }
        .success { border-color: green !important; }
        .error { border-color: red !important; }
        .file-item { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 ทดสอบการเข้าถึงรูปภาพโดยตรง</h1>
    <p><strong>ตำแหน่งไฟล์นี้:</strong> assets/img/services/test_direct_access.html</p>
    
    <h2>📁 ไฟล์รูปภาพในโฟลเดอร์นี้:</h2>
    <div id="file-list">
        <p>กำลังโหลดรายการไฟล์...</p>
    </div>
    
    <h2>🧪 ทดสอบการแสดงรูปภาพ:</h2>
    <div id="image-tests">
        <p>กำลังทดสอบ...</p>
    </div>
    
    <h2>🔗 ลิงก์ที่เกี่ยวข้อง:</h2>
    <ul>
        <li><a href="../../../admin/manage_services.php" target="_blank">📋 หน้าจัดการบริการ</a></li>
        <li><a href="../../../quick_image_fix.php" target="_blank">🚀 แก้ไขปัญหาด่วน</a></li>
        <li><a href="../../../admin/test_image_in_admin.php" target="_blank">🧪 ทดสอบจากโฟลเดอร์ admin</a></li>
    </ul>
    
    <script>
        // ทดสอบการเข้าถึงไฟล์รูปภาพ
        const testImages = [
            'service_1751607980_6943.jpg',
            'service_1751612690_5651.jpg',
            'service_1751612702_6779.jpg',
            'service_debug_1751611701_1610.jpg',
            'service_edit_1751612067_5513.jpg'
        ];
        
        function testImageAccess() {
            const container = document.getElementById('image-tests');
            container.innerHTML = '';
            
            testImages.forEach((filename, index) => {
                const div = document.createElement('div');
                div.className = 'file-item';
                div.innerHTML = `
                    <h4>ทดสอบ: ${filename}</h4>
                    <img src="${filename}" alt="Test ${index + 1}" class="test-image" 
                         onload="this.classList.add('success'); this.nextElementSibling.innerHTML='✅ โหลดสำเร็จ'; this.nextElementSibling.style.color='green';"
                         onerror="this.classList.add('error'); this.nextElementSibling.innerHTML='❌ โหลดไม่ได้'; this.nextElementSibling.style.color='red';">
                    <span style="margin-left: 10px; font-weight: bold;">⏳ กำลังโหลด...</span>
                    <br><a href="${filename}" target="_blank">เปิดรูปในแท็บใหม่</a>
                `;
                container.appendChild(div);
            });
        }
        
        function listFiles() {
            const container = document.getElementById('file-list');
            container.innerHTML = `
                <div class="file-item">
                    <h4>📋 ไฟล์ที่คาดว่าจะมี:</h4>
                    ${testImages.map(file => `
                        <div style="margin: 5px 0;">
                            <strong>${file}</strong> 
                            <a href="${file}" target="_blank" style="margin-left: 10px;">เปิดลิงก์</a>
                        </div>
                    `).join('')}
                </div>
                <div class="file-item">
                    <h4>💡 วิธีตรวจสอบ:</h4>
                    <ol>
                        <li>ดูว่ารูปไหนแสดงได้ (เส้นขอบสีเขียว)</li>
                        <li>ถ้าไม่มีรูปไหนแสดงได้ = ปัญหาการเข้าถึงโฟลเดอร์</li>
                        <li>ถ้าบางรูปแสดงได้ = ปัญหาไฟล์เฉพาะ</li>
                        <li>ถ้าทุกรูปแสดงได้ = ปัญหาอยู่ที่ path ในโค้ด</li>
                    </ol>
                </div>
            `;
        }
        
        // เริ่มการทดสอบ
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 เริ่มทดสอบการเข้าถึงรูปภาพโดยตรง');
            listFiles();
            testImageAccess();
        });
    </script>
</body>
</html>
