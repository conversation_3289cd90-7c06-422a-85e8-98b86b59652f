<?php
/**
 * ไฟล์สำหรับแสดงรูปภาพจากโฟลเดอร์ assets/img/services/
 * ใช้สำหรับแสดงรูปภาพบริการในระบบ
 */

// ตรวจสอบพารามิเตอร์ image
if (!isset($_GET['image']) || empty($_GET['image'])) {
    http_response_code(400);
    exit('ไม่พบชื่อไฟล์รูปภาพ');
}

$image_name = $_GET['image'];

// ตรวจสอบความปลอดภัยของชื่อไฟล์
if (strpos($image_name, '..') !== false || strpos($image_name, '/') !== false || strpos($image_name, '\\') !== false) {
    http_response_code(400);
    exit('ชื่อไฟล์ไม่ถูกต้อง');
}

// กำหนด path ของรูปภาพ
$image_path = __DIR__ . '/assets/img/services/' . $image_name;

// ตรวจสอบว่าไฟล์มีอยู่จริง
if (!file_exists($image_path)) {
    http_response_code(404);
    exit('ไม่พบไฟล์รูปภาพ');
}

// ตรวจสอบว่าเป็นไฟล์รูปภาพ
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$file_extension = strtolower(pathinfo($image_path, PATHINFO_EXTENSION));

if (!in_array($file_extension, $allowed_extensions)) {
    http_response_code(400);
    exit('ไฟล์ไม่ใช่รูปภาพ');
}

// กำหนด Content-Type ตามนามสกุลไฟล์
$content_types = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'webp' => 'image/webp'
];

$content_type = $content_types[$file_extension] ?? 'image/jpeg';

// ส่ง header และแสดงรูปภาพ
header('Content-Type: ' . $content_type);
header('Content-Length: ' . filesize($image_path));
header('Cache-Control: public, max-age=3600'); // Cache 1 ชั่วโมง
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($image_path)) . ' GMT');

// อ่านและแสดงไฟล์
readfile($image_path);
exit;
?>
