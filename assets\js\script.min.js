document.addEventListener('DOMContentLoaded',function(){initFormValidation();initTimeSlotSelection();initDatePicker();initConfirmDialogs();initTooltips();setTimeout(function(){const alerts=document.querySelectorAll('.alert');alerts.forEach(function(alert){alert.style.transition='opacity 0.5s';alert.style.opacity='0';setTimeout(function(){alert.remove()},500)})},5000)});function initFormValidation(){const forms=document.querySelectorAll('form');forms.forEach(function(form){form.addEventListener('submit',function(e){const requiredFields=form.querySelectorAll('[required]');let isValid=true;requiredFields.forEach(function(field){if(!field.value.trim()){isValid=false;field.classList.add('is-invalid');field.addEventListener('input',function(){if(this.value.trim()){this.classList.remove('is-invalid')}},{once:true})}});if(!isValid){e.preventDefault();showAlert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน','danger')}})})}function initTimeSlotSelection(){const timeSlots=document.querySelectorAll('.time-slot');timeSlots.forEach(function(slot){slot.addEventListener('click',function(){timeSlots.forEach(function(s){s.classList.remove('selected')});this.classList.add('selected');const hiddenInput=document.getElementById('selected_time');if(hiddenInput){hiddenInput.value=this.dataset.time}})})}function initDatePicker(){const dateInputs=document.querySelectorAll('input[type="date"]');dateInputs.forEach(function(input){const today=new Date().toISOString().split('T')[0];input.min=today;input.addEventListener('change',function(){if(this.value<today){this.value=today;showAlert('ไม่สามารถเลือกวันที่ในอดีตได้','warning')}})})}function initConfirmDialogs(){const deleteButtons=document.querySelectorAll('.btn-delete, .delete-btn');deleteButtons.forEach(function(btn){btn.addEventListener('click',function(e){if(!confirm('คุณแน่ใจหรือไม่ที่จะลบข้อมูลนี้?')){e.preventDefault()}})})}function initTooltips(){const tooltipElements=document.querySelectorAll('[data-bs-toggle="tooltip"]');if(typeof bootstrap!=='undefined'&&bootstrap.Tooltip){tooltipElements.forEach(function(element){new bootstrap.Tooltip(element)})}}function showAlert(message,type='info'){const alertContainer=document.getElementById('alert-container')||document.body;const alertDiv=document.createElement('div');alertDiv.className=`alert alert-${type} alert-dismissible fade show`;alertDiv.innerHTML=`${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;alertContainer.insertBefore(alertDiv,alertContainer.firstChild);setTimeout(function(){alertDiv.remove()},5000)}function updateTimeSlots(barberId,selectedDate){if(!barberId||!selectedDate)return;fetch(`get_available_times.php?barber_id=${barberId}&date=${selectedDate}`).then(response=>response.json()).then(data=>{const container=document.getElementById('time-slots-container');if(container){container.innerHTML='';if(data.success&&data.times.length>0){data.times.forEach(function(time){const slot=document.createElement('div');slot.className='time-slot';slot.dataset.time=time;slot.textContent=time+' น.';slot.addEventListener('click',function(){document.querySelectorAll('.time-slot').forEach(s=>s.classList.remove('selected'));this.classList.add('selected');const hiddenInput=document.getElementById('selected_time');if(hiddenInput)hiddenInput.value=time});container.appendChild(slot)})}else{container.innerHTML='<p class="text-muted">ไม่มีเวลาว่างในวันที่เลือก</p>'}}}).catch(error=>{console.error('Error:',error);showAlert('เกิดข้อผิดพลาดในการโหลดเวลาว่าง','danger')})}function toggleStatus(id,type){const confirmed=confirm('คุณต้องการเปลี่ยนสถานะหรือไม่?');if(!confirmed)return;fetch('toggle_status.php',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:`id=${id}&type=${type}&csrf_token=${getCSRFToken()}`}).then(response=>response.json()).then(data=>{if(data.success){location.reload()}else{showAlert(data.message||'เกิดข้อผิดพลาด','danger')}}).catch(error=>{console.error('Error:',error);showAlert('เกิดข้อผิดพลาดในการเชื่อมต่อ','danger')})}function getCSRFToken(){const tokenInput=document.querySelector('input[name="csrf_token"]');return tokenInput?tokenInput.value:''}function previewImage(input){if(input.files&&input.files[0]){const reader=new FileReader();reader.onload=function(e){const preview=document.getElementById('image-preview');if(preview){preview.src=e.target.result;preview.style.display='block'}};reader.readAsDataURL(input.files[0])}}function formatCurrency(amount){return new Intl.NumberFormat('th-TH',{style:'currency',currency:'THB'}).format(amount)}function formatDate(dateString){const date=new Date(dateString);return date.toLocaleDateString('th-TH',{year:'numeric',month:'long',day:'numeric'})}function formatTime(timeString){const[hours,minutes]=timeString.split(':');return `${hours}:${minutes} น.`}function copyToClipboard(text){navigator.clipboard.writeText(text).then(function(){showAlert('คัดลอกเรียบร้อยแล้ว','success')}).catch(function(){showAlert('ไม่สามารถคัดลอกได้','danger')})}function printElement(elementId){const element=document.getElementById(elementId);if(element){const printWindow=window.open('','_blank');printWindow.document.write(`<html><head><title>พิมพ์</title><style>body{font-family:Arial,sans-serif;margin:20px}@media print{body{margin:0}}</style></head><body>${element.innerHTML}</body></html>`);printWindow.document.close();printWindow.print()}}window.addEventListener('scroll',function(){const navbar=document.querySelector('.navbar');if(window.scrollY>50){navbar.classList.add('scrolled')}else{navbar.classList.remove('scrolled')}});document.querySelectorAll('a[href^="#"]').forEach(anchor=>{anchor.addEventListener('click',function(e){e.preventDefault();const target=document.querySelector(this.getAttribute('href'));if(target){target.scrollIntoView({behavior:'smooth',block:'start'})}})});document.querySelectorAll('.btn').forEach(btn=>{btn.addEventListener('click',function(e){if(this.href&&!this.href.includes('#')){const originalText=this.innerHTML;this.innerHTML='<span class="loading-spinner"></span> กำลังโหลด...';this.disabled=true;setTimeout(()=>{this.innerHTML=originalText;this.disabled=false},2000)}})});const observerOptions={threshold:0.1,rootMargin:'0px 0px -50px 0px'};const observer=new IntersectionObserver((entries)=>{entries.forEach(entry=>{if(entry.isIntersecting){entry.target.style.opacity='1';entry.target.style.transform='translateY(0)'}})},observerOptions);document.addEventListener('DOMContentLoaded',function(){document.querySelectorAll('.card, .feature-icon').forEach(el=>{el.style.opacity='0';el.style.transform='translateY(30px)';el.style.transition='all 0.6s ease-out';observer.observe(el)})});
