<?php
require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

// ดึงข้อมูลสถิติ
$total_users = fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'customer'")['count'];
$total_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings")['count'];
$pending_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE status = 'pending'")['count'];
$today_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE booking_date = CURDATE()")['count'];

// รายได้วันนี้
$today_revenue = fetchOne("SELECT COALESCE(SUM(total_price), 0) as revenue FROM bookings WHERE booking_date = CURDATE() AND status IN ('confirmed', 'completed')")['revenue'];

// รายได้เดือนนี้
$month_revenue = fetchOne("SELECT COALESCE(SUM(total_price), 0) as revenue FROM bookings WHERE MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE()) AND status IN ('confirmed', 'completed')")['revenue'];

// การจองล่าสุด
$recent_bookings = fetchAll("
    SELECT b.*, u.full_name as customer_name, s.name as service_name, br.name as barber_name
    FROM bookings b
    JOIN users u ON b.user_id = u.id
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    ORDER BY b.created_at DESC
    LIMIT 10
");

// การจองวันนี้
$today_bookings_list = fetchAll("
    SELECT b.*, u.full_name as customer_name, s.name as service_name, br.name as barber_name
    FROM bookings b
    JOIN users u ON b.user_id = u.id
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    WHERE b.booking_date = CURDATE()
    ORDER BY b.booking_time ASC
");

// สถิติบริการยอดนิยม
$popular_services = fetchAll("
    SELECT s.name, COUNT(b.id) as booking_count
    FROM services s
    LEFT JOIN bookings b ON s.id = b.service_id
    WHERE s.is_active = 1
    GROUP BY s.id, s.name
    ORDER BY booking_count DESC
    LIMIT 5
");
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ดแอดมิน - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_bookings.php">จัดการการจอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php">จัดการผู้ใช้</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_services.php">จัดการบริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_barbers.php">จัดการช่างตัดผม</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_gallery.php">จัดการแกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">รายงานและสถิติ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">ระบบแจ้งเตือน</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/index.php">ดูหน้าเว็บ</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h2 class="mb-3">
                        <i class="fas fa-tachometer-alt text-primary"></i> 
                        แดชบอร์ดแอดมิน
                    </h2>
                    <p class="text-muted">ยินดีต้อนรับ, <?= sanitize($_SESSION['full_name']) ?> | วันที่: <?= formatDateThai(date('Y-m-d')) ?></p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="dashboard-card stat-card bg-primary text-white">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stat-number"><?= $total_users ?></div>
                            <div class="stat-label">ลูกค้าทั้งหมด</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="dashboard-card stat-card bg-success text-white">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stat-number"><?= $total_bookings ?></div>
                            <div class="stat-label">การจองทั้งหมด</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="dashboard-card stat-card bg-warning text-white">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stat-number"><?= $pending_bookings ?></div>
                            <div class="stat-label">รอยืนยัน</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="dashboard-card stat-card bg-info text-white">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="stat-number"><?= $today_bookings ?></div>
                            <div class="stat-label">การจองวันนี้</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Card -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="dashboard-card stat-card bg-purple text-white">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                            $gallery_count = fetchOne("SELECT COUNT(*) as count FROM gallery WHERE is_active = 1")['count'];
                            $total_gallery = fetchOne("SELECT COUNT(*) as count FROM gallery")['count'];
                            ?>
                            <div class="stat-number"><?= $gallery_count ?></div>
                            <div class="stat-label">รูปภาพในแกลเลอรี่</div>
                            <small>ทั้งหมด <?= $total_gallery ?> รูป</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-images fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="manage_gallery.php" class="btn btn-light btn-sm">
                            <i class="fas fa-cog"></i> จัดการแกลเลอรี่
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5 class="mb-3"><i class="fas fa-images text-purple"></i> แกลเลอรี่ล่าสุด</h5>
                    <div class="row">
                        <?php
                        $recent_gallery = fetchAll("SELECT * FROM gallery WHERE is_active = 1 ORDER BY created_at DESC LIMIT 6");
                        if (empty($recent_gallery)):
                        ?>
                            <div class="col-12 text-center py-3">
                                <i class="fas fa-images fa-3x text-muted mb-2"></i>
                                <p class="text-muted">ยังไม่มีรูปภาพในแกลเลอรี่</p>
                                <a href="manage_gallery.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> เพิ่มรูปภาพแรก
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_gallery as $item): ?>
                                <div class="col-md-2 col-4 mb-2">
                                    <div class="gallery-thumb">
                                        <img src="../assets/img/gallery/<?= sanitize($item['image']) ?>"
                                             alt="<?= sanitize($item['title']) ?>"
                                             class="img-fluid rounded"
                                             style="width: 100%; height: 60px; object-fit: cover;"
                                             onerror="this.src='../assets/img/placeholder.jpg'">
                                        <div class="gallery-thumb-overlay">
                                            <small><?= sanitize($item['title']) ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Cards -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="dashboard-card stat-card bg-gradient-success text-white">
                    <h5 class="mb-3"><i class="fas fa-money-bill-wave"></i> รายได้วันนี้</h5>
                    <div class="stat-number"><?= number_format($today_revenue) ?></div>
                    <div class="stat-label">บาท</div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="dashboard-card stat-card bg-gradient-primary text-white">
                    <h5 class="mb-3"><i class="fas fa-chart-line"></i> รายได้เดือนนี้</h5>
                    <div class="stat-number"><?= number_format($month_revenue) ?></div>
                    <div class="stat-label">บาท</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Today's Bookings -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-calendar-day text-info"></i> 
                        การจองวันนี้ (<?= count($today_bookings_list) ?> รายการ)
                    </h5>
                    
                    <?php if (empty($today_bookings_list)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่มีการจองวันนี้</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>เวลา</th>
                                        <th>ลูกค้า</th>
                                        <th>บริการ</th>
                                        <th>ช่างตัดผม</th>
                                        <th>สถานะ</th>
                                        <th>ราคา</th>
                                        <th>การดำเนินการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($today_bookings_list as $booking): ?>
                                    <tr>
                                        <td><?= formatTime12Hour($booking['booking_time']) ?></td>
                                        <td><?= sanitize($booking['customer_name']) ?></td>
                                        <td><?= sanitize($booking['service_name']) ?></td>
                                        <td><?= sanitize($booking['barber_name']) ?></td>
                                        <td>
                                            <span class="badge status-<?= $booking['status'] ?>">
                                                <?php
                                                $status_text = [
                                                    'pending' => 'รอยืนยัน',
                                                    'confirmed' => 'ยืนยันแล้ว',
                                                    'completed' => 'เสร็จสิ้น',
                                                    'cancelled' => 'ยกเลิก'
                                                ];
                                                echo $status_text[$booking['status']];
                                                ?>
                                            </span>
                                        </td>
                                        <td><?= number_format($booking['total_price']) ?> บาท</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($booking['status'] === 'pending'): ?>
                                                    <button class="btn btn-success btn-sm" 
                                                            onclick="updateBookingStatus(<?= $booking['id'] ?>, 'confirmed')">
                                                        ยืนยัน
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <?php if ($booking['status'] === 'confirmed'): ?>
                                                    <button class="btn btn-info btn-sm" 
                                                            onclick="updateBookingStatus(<?= $booking['id'] ?>, 'completed')">
                                                        เสร็จสิ้น
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions & Stats -->
            <div class="col-lg-4">
                <!-- Quick Actions -->
                <div class="dashboard-card mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt text-warning"></i> 
                        การดำเนินการด่วน
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <a href="manage_bookings.php?status=pending" class="btn btn-warning">
                            <i class="fas fa-clock"></i> การจองรอยืนยัน (<?= $pending_bookings ?>)
                        </a>
                        
                        <a href="manage_bookings.php" class="btn btn-primary">
                            <i class="fas fa-calendar-alt"></i> จัดการการจองทั้งหมด
                        </a>
                        
                        <a href="manage_users.php" class="btn btn-info">
                            <i class="fas fa-users"></i> จัดการผู้ใช้งาน
                        </a>
                        
                        <a href="report.php" class="btn btn-success">
                            <i class="fas fa-chart-bar"></i> ดูรายงาน
                        </a>
                    </div>
                </div>

                <!-- Popular Services -->
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-star text-warning"></i> 
                        บริการยอดนิยม
                    </h5>
                    
                    <?php foreach ($popular_services as $service): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?= sanitize($service['name']) ?></span>
                            <span class="badge bg-primary"><?= $service['booking_count'] ?> ครั้ง</span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <?php if (!empty($recent_bookings)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-history text-secondary"></i> 
                        การจองล่าสุด
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>วันที่จอง</th>
                                    <th>ลูกค้า</th>
                                    <th>บริการ</th>
                                    <th>ช่างตัดผม</th>
                                    <th>วันที่นัด</th>
                                    <th>เวลา</th>
                                    <th>สถานะ</th>
                                    <th>ราคา</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_bookings as $booking): ?>
                                <tr>
                                    <td><?= date('d/m/Y H:i', strtotime($booking['created_at'])) ?></td>
                                    <td><?= sanitize($booking['customer_name']) ?></td>
                                    <td><?= sanitize($booking['service_name']) ?></td>
                                    <td><?= sanitize($booking['barber_name']) ?></td>
                                    <td><?= formatDateThai($booking['booking_date']) ?></td>
                                    <td><?= formatTime12Hour($booking['booking_time']) ?></td>
                                    <td>
                                        <span class="badge status-<?= $booking['status'] ?>">
                                            <?php
                                            $status_text = [
                                                'pending' => 'รอยืนยัน',
                                                'confirmed' => 'ยืนยันแล้ว',
                                                'completed' => 'เสร็จสิ้น',
                                                'cancelled' => 'ยกเลิก'
                                            ];
                                            echo $status_text[$booking['status']];
                                            ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($booking['total_price']) ?> บาท</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
