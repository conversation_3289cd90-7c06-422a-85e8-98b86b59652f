<?php
/**
 * ฟังก์ชันช่วยเหลือต่างๆ
 * Helper Functions
 */

require_once __DIR__ . '/../config/db.php';

/**
 * ฟังก์ชันเชื่อมต่อฐานข้อมูล
 */
function getConnection() {
    global $pdo;
    if (!$pdo) {
        // ใช้ค่าจาก config/db.php
        $host = DB_HOST;
        $dbname = DB_NAME;
        $username = DB_USER;
        $password = DB_PASS;

        try {
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception("การเชื่อมต่อฐานข้อมูลล้มเหลว: " . $e->getMessage());
        }
    }
    return $pdo;
}

/**
 * ฟังก์ชันดึงข้อมูลหลายแถว
 */
function fetchAll($sql, $params = []) {
    $pdo = getConnection();
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * ฟังก์ชันดึงข้อมูลแถวเดียว
 */
function fetchOne($sql, $params = []) {
    $pdo = getConnection();
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetch();
}

/**
 * ฟังก์ชันรันคำสั่ง SQL
 */
function executeQuery($sql, $params = []) {
    $pdo = getConnection();
    $stmt = $pdo->prepare($sql);
    return $stmt->execute($params);
}

/**
 * ฟังก์ชันเข้าสู่ระบบ
 */
function login($email, $password) {
    // ตรวจสอบว่าตารางมีคอลัมน์ username หรือไม่
    try {
        $pdo = getConnection();
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $hasUsername = in_array('username', $columns);

        if ($hasUsername) {
            // ถ้ามีคอลัมน์ username ให้ค้นหาทั้ง username และ email
            $sql = "SELECT * FROM users WHERE username = ? OR email = ?";
            $user = fetchOne($sql, [$email, $email]);
        } else {
            // ถ้าไม่มีคอลัมน์ username ให้ค้นหาแค่ email
            $sql = "SELECT * FROM users WHERE email = ?";
            $user = fetchOne($sql, [$email]);
        }

        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $hasUsername ? $user['username'] : $user['email'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['role'] = $user['role'];
            return true;
        }
        return false;

    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

/**
 * ฟังก์ชันสมัครสมาชิก
 */
function register($username, $email, $password, $full_name, $phone) {
    try {
        // ตรวจสอบว่าตารางมีคอลัมน์ username หรือไม่
        $pdo = getConnection();
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $hasUsername = in_array('username', $columns);

        if ($hasUsername) {
            // ตรวจสอบว่ามี username หรือ email ซ้ำหรือไม่
            $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
            $existing = fetchOne($sql, [$username, $email]);

            if ($existing) {
                return false; // มีผู้ใช้งานอยู่แล้ว
            }

            // เข้ารหัสรหัสผ่าน
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // บันทึกข้อมูลผู้ใช้ใหม่ (มีคอลัมน์ username)
            $sql = "INSERT INTO users (username, email, password, full_name, phone) VALUES (?, ?, ?, ?, ?)";
            $result = executeQuery($sql, [$username, $email, $hashed_password, $full_name, $phone]);
        } else {
            // ตรวจสอบว่ามี email ซ้ำหรือไม่
            $sql = "SELECT id FROM users WHERE email = ?";
            $existing = fetchOne($sql, [$email]);

            if ($existing) {
                return false; // มีผู้ใช้งานอยู่แล้ว
            }

            // เข้ารหัสรหัสผ่าน
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // บันทึกข้อมูลผู้ใช้ใหม่ (ไม่มีคอลัมน์ username)
            $sql = "INSERT INTO users (email, password, full_name, phone) VALUES (?, ?, ?, ?)";
            $result = executeQuery($sql, [$email, $hashed_password, $full_name, $phone]);
        }

        return $result !== false;

    } catch (Exception $e) {
        error_log("Register error: " . $e->getMessage());
        return false;
    }
}

/**
 * ตรวจสอบว่าผู้ใช้เข้าสู่ระบบหรือไม่
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * ตรวจสอบสิทธิ์ผู้ดูแลระบบ
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * ออกจากระบบ
 */
function logout() {
    session_destroy();
    header('Location: /barber_booking/public/index.php');
    exit;
}

/**
 * ป้องกัน XSS
 */
function sanitize($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

/**
 * ตรวจสอบรูปแบบอีเมล
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * ตรวจสอบรูปแบบเบอร์โทรศัพท์
 */
function isValidPhone($phone) {
    return preg_match('/^[0-9\-\+\(\)\s]+$/', $phone);
}

/**
 * แปลงวันที่เป็นรูปแบบไทย
 */
function formatDateThai($date) {
    $thai_months = [
        '01' => 'มกราคม', '02' => 'กุมภาพันธ์', '03' => 'มีนาคม',
        '04' => 'เมษายน', '05' => 'พฤษภาคม', '06' => 'มิถุนายน',
        '07' => 'กรกฎาคม', '08' => 'สิงหาคม', '09' => 'กันยายน',
        '10' => 'ตุลาคม', '11' => 'พฤศจิกายน', '12' => 'ธันวาคม'
    ];
    
    $date_parts = explode('-', $date);
    if (count($date_parts) == 3) {
        $year = $date_parts[0] + 543; // แปลงเป็น พ.ศ.
        $month = $thai_months[$date_parts[1]];
        $day = intval($date_parts[2]);
        return "$day $month $year";
    }
    return $date;
}

/**
 * แปลงเวลาเป็นรูปแบบ 12 ชั่วโมง
 */
function formatTime12Hour($time) {
    return date('g:i A', strtotime($time));
}

/**
 * แปลงตัวเลขเป็นตัวอักษรไทย
 */
function convertNumberToThaiText($number) {
    $number = (int)$number;

    if ($number == 0) return "ศูนย์";

    $ones = ["", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า"];
    $tens = ["", "", "ยี่สิบ", "สามสิบ", "สี่สิบ", "ห้าสิบ", "หกสิบ", "เจ็ดสิบ", "แปดสิบ", "เก้าสิบ"];
    $hundreds = ["", "หนึ่งร้อย", "สองร้อย", "สามร้อย", "สี่ร้อย", "ห้าร้อย", "หกร้อย", "เจ็ดร้อย", "แปดร้อย", "เก้าร้อย"];
    $thousands = ["", "หนึ่งพัน", "สองพัน", "สามพัน", "สี่พัน", "ห้าพัน", "หกพัน", "เจ็ดพัน", "แปดพัน", "เก้าพัน"];

    $result = "";

    // หมื่น
    if ($number >= 10000) {
        $ten_thousands = intval($number / 10000);
        if ($ten_thousands == 1) {
            $result .= "หนึ่งหมื่น";
        } else if ($ten_thousands == 2) {
            $result .= "สองหมื่น";
        } else {
            $result .= $ones[$ten_thousands] . "หมื่น";
        }
        $number = $number % 10000;
    }

    // พัน
    if ($number >= 1000) {
        $thousand = intval($number / 1000);
        $result .= $thousands[$thousand];
        $number = $number % 1000;
    }

    // ร้อย
    if ($number >= 100) {
        $hundred = intval($number / 100);
        $result .= $hundreds[$hundred];
        $number = $number % 100;
    }

    // สิบ
    if ($number >= 20) {
        $ten = intval($number / 10);
        $result .= $tens[$ten];
        $number = $number % 10;
    } else if ($number >= 10) {
        if ($number == 10) {
            $result .= "สิบ";
        } else {
            $result .= "สิบ" . $ones[$number % 10];
        }
        $number = 0;
    }

    // หน่วย
    if ($number > 0) {
        if ($number == 1 && strlen($result) > 0) {
            $result .= "เอ็ด";
        } else {
            $result .= $ones[$number];
        }
    }

    return $result;
}

/**
 * ตรวจสอบว่าวันที่และเวลาว่างหรือไม่
 */
function isTimeSlotAvailable($barber_id, $date, $time, $duration, $exclude_booking_id = null) {
    $start_time = $time;
    $end_time = date('H:i:s', strtotime($time) + ($duration * 60));
    
    $sql = "SELECT id FROM bookings 
            WHERE barber_id = ? 
            AND booking_date = ? 
            AND status NOT IN ('cancelled') 
            AND (
                (booking_time <= ? AND DATE_ADD(CONCAT(booking_date, ' ', booking_time), INTERVAL (SELECT duration FROM services WHERE id = bookings.service_id) MINUTE) > ?)
                OR (booking_time < ? AND booking_time >= ?)
            )";
    
    $params = [$barber_id, $date, $start_time, $start_time, $end_time, $start_time];
    
    if ($exclude_booking_id) {
        $sql .= " AND id != ?";
        $params[] = $exclude_booking_id;
    }
    
    $existing = fetchOne($sql, $params);
    return !$existing;
}

/**
 * คำนวณราคาหลังหักส่วนลด
 */
function calculateDiscountedPrice($original_price, $promotion_id = null) {
    if (!$promotion_id) {
        return $original_price;
    }
    
    $sql = "SELECT * FROM promotions WHERE id = ? AND is_active = 1 AND start_date <= CURDATE() AND end_date >= CURDATE()";
    $promotion = fetchOne($sql, [$promotion_id]);
    
    if (!$promotion) {
        return $original_price;
    }
    
    if ($promotion['discount_percent']) {
        return $original_price * (1 - $promotion['discount_percent'] / 100);
    } elseif ($promotion['discount_amount']) {
        return max(0, $original_price - $promotion['discount_amount']);
    }
    
    return $original_price;
}



/**
 * ส่งอีเมลยืนยันการจอง
 */
function sendBookingConfirmation($booking_id) {
    // ดึงข้อมูลการจอง
    $sql = "SELECT b.*, u.full_name as customer_name, u.email as customer_email,
                   s.name as service_name, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";

    $booking = fetchOne($sql, [$booking_id]);

    if (!$booking) {
        return false;
    }

    $subject = "ยืนยันการจองคิว #{$booking['id']} - ร้านตัดผม Barber Shop";
    $message = "เรียน คุณ{$booking['customer_name']}\n\n";
    $message .= "การจองคิวของท่านได้รับการยืนยันแล้ว\n\n";
    $message .= "รายละเอียดการจอง:\n";
    $message .= "หมายเลขการจอง: #{$booking['id']}\n";
    $message .= "บริการ: {$booking['service_name']}\n";
    $message .= "ช่างตัดผม: {$booking['barber_name']}\n";
    $message .= "วันที่: " . formatDateThai($booking['booking_date']) . "\n";
    $message .= "เวลา: " . formatTime12Hour($booking['booking_time']) . "\n";
    $message .= "ราคา: " . number_format($booking['total_price']) . " บาท\n\n";
    $message .= "ขอบคุณที่เลือกใช้บริการของเรา\n";
    $message .= "ร้านตัดผม Barber Shop";

    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    return mail($booking['customer_email'], $subject, $message, $headers);
}

/**
 * ส่งอีเมลแจ้งเตือนการเปลี่ยนสถานะ
 */
function sendBookingStatusUpdate($booking_id, $new_status) {
    $sql = "SELECT b.*, u.full_name as customer_name, u.email as customer_email,
                   s.name as service_name, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";

    $booking = fetchOne($sql, [$booking_id]);

    if (!$booking) {
        return false;
    }

    $status_text = [
        'confirmed' => 'ยืนยันแล้ว',
        'completed' => 'เสร็จสิ้น',
        'cancelled' => 'ยกเลิก'
    ];

    $status = $status_text[$new_status] ?? $new_status;

    $subject = "อัพเดทสถานะการจอง #{$booking['id']} - {$status}";
    $message = "เรียน คุณ{$booking['customer_name']}\n\n";
    $message .= "สถานะการจองของท่านได้รับการอัพเดท\n\n";
    $message .= "รายละเอียดการจอง:\n";
    $message .= "หมายเลขการจอง: #{$booking['id']}\n";
    $message .= "บริการ: {$booking['service_name']}\n";
    $message .= "ช่างตัดผม: {$booking['barber_name']}\n";
    $message .= "วันที่: " . formatDateThai($booking['booking_date']) . "\n";
    $message .= "เวลา: " . formatTime12Hour($booking['booking_time']) . "\n";
    $message .= "สถานะใหม่: {$status}\n\n";
    $message .= "ขอบคุณที่เลือกใช้บริการของเรา\n";
    $message .= "ร้านตัดผม Barber Shop";

    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    return mail($booking['customer_email'], $subject, $message, $headers);
}

/**
 * สร้าง CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * ตรวจสอบ CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
?>
