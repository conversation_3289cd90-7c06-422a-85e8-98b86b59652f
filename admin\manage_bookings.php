<?php
require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ตัวกรองสถานะ
$status_filter = $_GET['status'] ?? 'all';
$date_filter = $_GET['date'] ?? 'all';

// สร้าง WHERE clause
$where_conditions = ["1=1"];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "b.status = ?";
    $params[] = $status_filter;
}

if ($date_filter === 'today') {
    $where_conditions[] = "b.booking_date = CURDATE()";
} elseif ($date_filter === 'tomorrow') {
    $where_conditions[] = "b.booking_date = DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
} elseif ($date_filter === 'week') {
    $where_conditions[] = "b.booking_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
}

$where_clause = implode(' AND ', $where_conditions);

// ดึงข้อมูลการจอง
$bookings = fetchAll("
    SELECT b.*, u.full_name as customer_name, u.email as customer_email, u.phone as customer_phone,
           s.name as service_name, s.duration, br.name as barber_name
    FROM bookings b
    JOIN users u ON b.user_id = u.id
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    WHERE $where_clause
    ORDER BY b.booking_date DESC, b.booking_time DESC
", $params);

// นับจำนวนตามสถานะ
$status_counts = [
    'all' => fetchOne("SELECT COUNT(*) as count FROM bookings")['count'],
    'pending' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE status = 'pending'")['count'],
    'confirmed' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE status = 'confirmed'")['count'],
    'completed' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE status = 'completed'")['count'],
    'cancelled' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE status = 'cancelled'")['count']
];

// ตรวจสอบการอัพเดทสถานะ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $booking_id = $_POST['booking_id'] ?? '';
    $new_status = $_POST['new_status'] ?? '';
    
    if ($booking_id && $new_status) {
        $sql = "UPDATE bookings SET status = ?, updated_at = NOW() WHERE id = ?";
        $result = executeQuery($sql, [$new_status, $booking_id]);
        
        if ($result) {
            // ส่งอีเมลแจ้งเตือน
            sendBookingStatusUpdate($booking_id, $new_status);
            $success_message = 'อัพเดทสถานะเรียบร้อยแล้ว';
            
            // Refresh หน้า
            header("Location: manage_bookings.php?status=$status_filter&date=$date_filter&success=1");
            exit;
        } else {
            $error_message = 'เกิดข้อผิดพลาดในการอัพเดทสถานะ';
        }
    }
}

if (isset($_GET['success'])) {
    $success_message = 'อัพเดทสถานะเรียบร้อยแล้ว';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการการจอง - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_bookings.php">จัดการการจอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php">จัดการผู้ใช้</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_services.php">จัดการบริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_barbers.php">จัดการช่างตัดผม</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">รายงาน</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/index.php">ดูหน้าเว็บ</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-calendar-alt text-primary"></i> จัดการการจอง
                </h2>
                <p class="text-muted">จัดการและติดตามสถานะการจองคิวทั้งหมด</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-success" onclick="exportBookings()">
                    <i class="fas fa-download"></i> ส่งออกข้อมูล
                </button>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="card-title mb-3">กรองตามสถานะ</h6>
                                <div class="btn-group" role="group">
                                    <a href="?status=all&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'all' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        ทั้งหมด (<?= $status_counts['all'] ?>)
                                    </a>
                                    <a href="?status=pending&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'pending' ? 'btn-warning' : 'btn-outline-warning' ?>">
                                        รอยืนยัน (<?= $status_counts['pending'] ?>)
                                    </a>
                                    <a href="?status=confirmed&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'confirmed' ? 'btn-success' : 'btn-outline-success' ?>">
                                        ยืนยันแล้ว (<?= $status_counts['confirmed'] ?>)
                                    </a>
                                    <a href="?status=completed&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'completed' ? 'btn-info' : 'btn-outline-info' ?>">
                                        เสร็จสิ้น (<?= $status_counts['completed'] ?>)
                                    </a>
                                    <a href="?status=cancelled&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'cancelled' ? 'btn-danger' : 'btn-outline-danger' ?>">
                                        ยกเลิก (<?= $status_counts['cancelled'] ?>)
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="card-title mb-3">กรองตามวันที่</h6>
                                <div class="btn-group" role="group">
                                    <a href="?status=<?= $status_filter ?>&date=all" 
                                       class="btn <?= $date_filter === 'all' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        ทั้งหมด
                                    </a>
                                    <a href="?status=<?= $status_filter ?>&date=today" 
                                       class="btn <?= $date_filter === 'today' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        วันนี้
                                    </a>
                                    <a href="?status=<?= $status_filter ?>&date=tomorrow" 
                                       class="btn <?= $date_filter === 'tomorrow' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        พรุ่งนี้
                                    </a>
                                    <a href="?status=<?= $status_filter ?>&date=week" 
                                       class="btn <?= $date_filter === 'week' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        สัปดาห์นี้
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="search-bookings" placeholder="ค้นหาการจอง...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bookings Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($bookings)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                                <h4>ไม่มีการจองที่ตรงกับเงื่อนไข</h4>
                                <p class="text-muted">ลองเปลี่ยนตัวกรองเพื่อดูข้อมูลอื่น</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="bookings-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>วันที่จอง</th>
                                            <th>ลูกค้า</th>
                                            <th>ติดต่อ</th>
                                            <th>บริการ</th>
                                            <th>ช่างตัดผม</th>
                                            <th>วันที่นัด</th>
                                            <th>เวลา</th>
                                            <th>สถานะ</th>
                                            <th>ราคา</th>
                                            <th>การดำเนินการ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($bookings as $booking): ?>
                                        <tr>
                                            <td>#<?= $booking['id'] ?></td>
                                            <td><?= date('d/m/Y H:i', strtotime($booking['created_at'])) ?></td>
                                            <td>
                                                <strong><?= sanitize($booking['customer_name']) ?></strong>
                                                <?php if ($booking['notes']): ?>
                                                    <br><small class="text-muted">หมายเหตุ: <?= sanitize($booking['notes']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="fas fa-envelope"></i> <?= sanitize($booking['customer_email']) ?><br>
                                                    <?php if ($booking['customer_phone']): ?>
                                                        <i class="fas fa-phone"></i> <?= sanitize($booking['customer_phone']) ?>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?= sanitize($booking['service_name']) ?><br>
                                                <small class="text-muted"><?= $booking['duration'] ?> นาที</small>
                                            </td>
                                            <td><?= sanitize($booking['barber_name']) ?></td>
                                            <td><?= formatDateThai($booking['booking_date']) ?></td>
                                            <td><?= formatTime12Hour($booking['booking_time']) ?></td>
                                            <td>
                                                <span class="badge status-<?= $booking['status'] ?>">
                                                    <?php
                                                    $status_text = [
                                                        'pending' => 'รอยืนยัน',
                                                        'confirmed' => 'ยืนยันแล้ว',
                                                        'completed' => 'เสร็จสิ้น',
                                                        'cancelled' => 'ยกเลิก'
                                                    ];
                                                    echo $status_text[$booking['status']];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?= number_format($booking['total_price']) ?> บาท</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="../customer/booking_detail.php?id=<?= $booking['id'] ?>"
                                                       class="btn btn-outline-info"
                                                       target="_blank"
                                                       title="ดูรายละเอียดการจอง">
                                                        <i class="fas fa-eye"></i>
                                                    </a>

                                                    <button class="btn btn-outline-warning"
                                                            onclick="editBooking(<?= $booking['id'] ?>)"
                                                            title="แก้ไขการจอง">
                                                        <i class="fas fa-edit"></i>
                                                    </button>

                                                    <?php if ($booking['status'] === 'pending'): ?>
                                                        <button class="btn btn-success" 
                                                                onclick="updateStatus(<?= $booking['id'] ?>, 'confirmed')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-danger" 
                                                                onclick="updateStatus(<?= $booking['id'] ?>, 'cancelled')">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($booking['status'] === 'confirmed'): ?>
                                                        <button class="btn btn-info" 
                                                                onclick="updateStatus(<?= $booking['id'] ?>, 'completed')">
                                                            <i class="fas fa-check-double"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Details Modal -->
    <div class="modal fade" id="bookingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดการจอง</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        function updateStatus(bookingId, newStatus) {
            const statusText = {
                'confirmed': 'ยืนยัน',
                'cancelled': 'ยกเลิก',
                'completed': 'เสร็จสิ้น'
            };
            
            if (confirm(`คุณแน่ใจหรือไม่ที่จะ${statusText[newStatus]}การจองนี้?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="booking_id" value="${bookingId}">
                    <input type="hidden" name="new_status" value="${newStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function editBooking(bookingId) {
            // เปิดหน้าแก้ไขการจองในแท็บใหม่
            window.open(`edit_booking.php?id=${bookingId}`, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        function exportBookings() {
            window.open('export_bookings.php?status=<?= $status_filter ?>&date=<?= $date_filter ?>', '_blank');
        }
    </script>
</body>
</html>
