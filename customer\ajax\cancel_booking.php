<?php
/**
 * ไฟล์ AJAX สำหรับยกเลิกการจอง
 */

require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/auth.php';

header('Content-Type: application/json');

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

$booking_id = $_POST['booking_id'] ?? '';

// ตรวจสอบข้อมูลที่จำเป็น
if (empty($booking_id)) {
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน']);
    exit;
}

// ตรวจสอบว่าการจองนี้เป็นของผู้ใช้ที่เข้าสู่ระบบหรือไม่
$booking = fetchOne("SELECT * FROM bookings WHERE id = ? AND user_id = ?", [$booking_id, $_SESSION['user_id']]);

if (!$booking) {
    echo json_encode(['success' => false, 'message' => 'ไม่พบการจองที่ระบุ']);
    exit;
}

// ตรวจสอบสถานะการจอง (สามารถยกเลิกได้เฉพาะสถานะ pending)
if ($booking['status'] !== 'pending') {
    echo json_encode(['success' => false, 'message' => 'ไม่สามารถยกเลิกการจองนี้ได้']);
    exit;
}

// ตรวจสอบเวลา (ไม่สามารถยกเลิกได้หากเหลือเวลาน้อยกว่า 2 ชั่วโมง)
$booking_datetime = $booking['booking_date'] . ' ' . $booking['booking_time'];
$time_diff = strtotime($booking_datetime) - time();

if ($time_diff < 7200) { // 2 ชั่วโมง = 7200 วินาที
    echo json_encode(['success' => false, 'message' => 'ไม่สามารถยกเลิกการจองได้ เนื่องจากเหลือเวลาน้อยกว่า 2 ชั่วโมง']);
    exit;
}

// อัพเดทสถานะเป็น cancelled
$sql = "UPDATE bookings SET status = 'cancelled', updated_at = NOW() WHERE id = ?";
$result = executeQuery($sql, [$booking_id]);

if ($result) {
    // ส่งอีเมลแจ้งเตือน
    sendBookingStatusUpdate($booking_id, 'cancelled');
    
    echo json_encode(['success' => true, 'message' => 'ยกเลิกการจองเรียบร้อยแล้ว']);
} else {
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการยกเลิกการจอง']);
}
?>
