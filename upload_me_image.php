<?php
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_FILES['me_image']) && $_FILES['me_image']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['me_image'];
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        
        if (in_array($file['type'], $allowed_types)) {
            $upload_path = __DIR__ . '/assets/img/me.jpg';
            
            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                $message = "<div class='alert alert-success'>✅ อัปโหลดรูปภาพ me.jpg สำเร็จ!</div>";
                
                // อัปเดต index.php ให้ใช้ไฟล์ .jpg
                $index_content = file_get_contents(__DIR__ . '/public/index.php');
                $index_content = str_replace("'../assets/img/me.svg'", "'../assets/img/me.jpg'", $index_content);
                file_put_contents(__DIR__ . '/public/index.php', $index_content);
                
                $message .= "<div class='alert alert-info'>📝 อัปเดต index.php ให้ใช้ไฟล์ .jpg แล้ว</div>";
            } else {
                $message = "<div class='alert alert-danger'>❌ ไม่สามารถอัปโหลดไฟล์ได้</div>";
            }
        } else {
            $message = "<div class='alert alert-danger'>❌ รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น</div>";
        }
    } else {
        $message = "<div class='alert alert-danger'>❌ กรุณาเลือกไฟล์รูปภาพ</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>อัปโหลดรูปภาพ me.jpg</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>อัปโหลดรูปภาพ me.jpg สำหรับหน้า Index</h2>
        
        <?= $message ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>อัปโหลดรูปภาพใหม่</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="me_image" class="form-label">เลือกรูปภาพ me.jpg</label>
                                <input type="file" class="form-control" id="me_image" name="me_image" accept="image/*" required>
                                <div class="form-text">รองรับไฟล์ JPG, PNG, GIF ขนาดแนะนำ 500x500 พิกเซล</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">อัปโหลด</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>รูปภาพปัจจุบัน</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        $current_image = 'assets/img/me.jpg';
                        $current_svg = 'assets/img/me.svg';
                        
                        if (file_exists($current_image)) {
                            echo "<img src='{$current_image}' alt='Current me.jpg' class='img-fluid' style='max-height: 300px;'>";
                            echo "<p class='mt-2 text-success'>✅ ใช้ไฟล์ me.jpg</p>";
                        } elseif (file_exists($current_svg)) {
                            echo "<img src='{$current_svg}' alt='Current me.svg' class='img-fluid' style='max-height: 300px;'>";
                            echo "<p class='mt-2 text-info'>📝 ใช้ไฟล์ SVG placeholder</p>";
                        } else {
                            echo "<p class='text-muted'>ไม่มีรูปภาพ</p>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <hr>
        <div class="d-flex gap-2">
            <a href="public/index.php" class="btn btn-success">ดูหน้า Index</a>
            <a href="admin/manage_gallery.php" class="btn btn-secondary">จัดการแกลเลอรี่</a>
        </div>
        
        <div class="mt-4">
            <h5>หมายเหตุ:</h5>
            <ul>
                <li>ไฟล์จะถูกบันทึกเป็น <code>assets/img/me.jpg</code></li>
                <li>ขนาดที่แนะนำคือ 500x500 พิกเซล หรือสัดส่วนสี่เหลี่ยมจัตุรัส</li>
                <li>ไฟล์จะแทนที่ไฟล์เดิม (หากมี)</li>
                <li>หน้า index.php จะอัปเดตอัตโนมัติให้ใช้ไฟล์ .jpg</li>
            </ul>
        </div>
    </div>
</body>
</html>
