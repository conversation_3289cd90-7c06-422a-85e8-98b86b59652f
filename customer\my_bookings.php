<?php
require_once __DIR__ . '/../includes/auth.php';
requireCustomer();

$user_id = $_SESSION['user_id'];

// ตัวกรองสถานะ
$status_filter = $_GET['status'] ?? 'all';
$date_filter = $_GET['date'] ?? 'all';

// สร้าง WHERE clause
$where_conditions = ["b.user_id = ?"];
$params = [$user_id];

if ($status_filter !== 'all') {
    $where_conditions[] = "b.status = ?";
    $params[] = $status_filter;
}

if ($date_filter === 'upcoming') {
    $where_conditions[] = "b.booking_date >= CURDATE()";
} elseif ($date_filter === 'past') {
    $where_conditions[] = "b.booking_date < CURDATE()";
}

$where_clause = implode(' AND ', $where_conditions);

// ดึงข้อมูลการจอง
$bookings = fetchAll("
    SELECT b.*, s.name as service_name, s.duration, br.name as barber_name 
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    WHERE $where_clause
    ORDER BY b.booking_date DESC, b.booking_time DESC
", $params);

// นับจำนวนตามสถานะ
$status_counts = [
    'all' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ?", [$user_id])['count'],
    'pending' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'pending'", [$user_id])['count'],
    'confirmed' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'confirmed'", [$user_id])['count'],
    'completed' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'completed'", [$user_id])['count'],
    'cancelled' => fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'cancelled'", [$user_id])['count']
];
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การจองของฉัน - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book.php">จองคิว</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="my_bookings.php">การจองของฉัน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../public/services.php">บริการ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-list text-primary"></i> การจองของฉัน
                </h2>
                <p class="text-muted">จัดการและติดตามสถานะการจองคิวของคุณ</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="book.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> จองคิวใหม่
                </a>
            </div>
        </div>

        <!-- Status Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="card-title mb-3">กรองตามสถานะ</h6>
                                <div class="btn-group" role="group">
                                    <a href="?status=all&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'all' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        ทั้งหมด (<?= $status_counts['all'] ?>)
                                    </a>
                                    <a href="?status=pending&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'pending' ? 'btn-warning' : 'btn-outline-warning' ?>">
                                        รอยืนยัน (<?= $status_counts['pending'] ?>)
                                    </a>
                                    <a href="?status=confirmed&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'confirmed' ? 'btn-success' : 'btn-outline-success' ?>">
                                        ยืนยันแล้ว (<?= $status_counts['confirmed'] ?>)
                                    </a>
                                    <a href="?status=completed&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'completed' ? 'btn-info' : 'btn-outline-info' ?>">
                                        เสร็จสิ้น (<?= $status_counts['completed'] ?>)
                                    </a>
                                    <a href="?status=cancelled&date=<?= $date_filter ?>" 
                                       class="btn <?= $status_filter === 'cancelled' ? 'btn-danger' : 'btn-outline-danger' ?>">
                                        ยกเลิก (<?= $status_counts['cancelled'] ?>)
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="card-title mb-3">กรองตามวันที่</h6>
                                <div class="btn-group" role="group">
                                    <a href="?status=<?= $status_filter ?>&date=all" 
                                       class="btn <?= $date_filter === 'all' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        ทั้งหมด
                                    </a>
                                    <a href="?status=<?= $status_filter ?>&date=upcoming" 
                                       class="btn <?= $date_filter === 'upcoming' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        ที่จะมาถึง
                                    </a>
                                    <a href="?status=<?= $status_filter ?>&date=past" 
                                       class="btn <?= $date_filter === 'past' ? 'btn-secondary' : 'btn-outline-secondary' ?>">
                                        ที่ผ่านมา
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bookings List -->
        <div class="row">
            <div class="col-12">
                <?php if (empty($bookings)): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                            <h4>ไม่มีการจองที่ตรงกับเงื่อนไข</h4>
                            <p class="text-muted mb-4">ลองเปลี่ยนตัวกรองหรือจองคิวใหม่</p>
                            <a href="book.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> จองคิวใหม่
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($bookings as $booking): ?>
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="text-center">
                                            <div class="h4 text-primary mb-0">
                                                <?= date('d', strtotime($booking['booking_date'])) ?>
                                            </div>
                                            <div class="text-muted">
                                                <?= date('M Y', strtotime($booking['booking_date'])) ?>
                                            </div>
                                            <div class="mt-2">
                                                <span class="badge status-<?= $booking['status'] ?>">
                                                    <?php
                                                    $status_text = [
                                                        'pending' => 'รอยืนยัน',
                                                        'confirmed' => 'ยืนยันแล้ว',
                                                        'completed' => 'เสร็จสิ้น',
                                                        'cancelled' => 'ยกเลิก'
                                                    ];
                                                    echo $status_text[$booking['status']];
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h5 class="mb-2"><?= sanitize($booking['service_name']) ?></h5>
                                        <p class="mb-1">
                                            <i class="fas fa-user-tie text-primary"></i> 
                                            ช่าง: <?= sanitize($booking['barber_name']) ?>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-clock text-primary"></i> 
                                            เวลา: <?= formatTime12Hour($booking['booking_time']) ?>
                                            (<?= $booking['duration'] ?> นาที)
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-calendar text-primary"></i> 
                                            วันที่จอง: <?= date('d/m/Y H:i', strtotime($booking['created_at'])) ?>
                                        </p>
                                        <?php if ($booking['notes']): ?>
                                            <p class="mb-0">
                                                <i class="fas fa-sticky-note text-primary"></i> 
                                                หมายเหตุ: <?= sanitize($booking['notes']) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-2 text-center">
                                        <div class="h4 text-success mb-0">
                                            <?= number_format($booking['total_price']) ?>
                                        </div>
                                        <div class="text-muted">บาท</div>
                                    </div>
                                    
                                    <div class="col-md-2 text-end">
                                        <div class="btn-group-vertical" role="group">
                                            <a href="booking_detail.php?id=<?= $booking['id'] ?>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> ดูรายละเอียด
                                            </a>
                                            
                                            <?php if ($booking['status'] === 'pending'): ?>
                                                <button type="button" 
                                                        class="btn btn-outline-danger btn-sm mt-1"
                                                        onclick="cancelBooking(<?= $booking['id'] ?>)">
                                                    <i class="fas fa-times"></i> ยกเลิก
                                                </button>
                                            <?php endif; ?>
                                            
                                            <?php if ($booking['status'] === 'completed'): ?>
                                                <a href="book.php?service_id=<?= $booking['service_id'] ?>" 
                                                   class="btn btn-outline-success btn-sm mt-1">
                                                    <i class="fas fa-redo"></i> จองซ้ำ
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการยกเลิก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณแน่ใจหรือไม่ที่จะยกเลิกการจองนี้?</p>
                    <p class="text-muted">การยกเลิกจะไม่สามารถย้อนกลับได้</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ไม่ยกเลิก</button>
                    <button type="button" class="btn btn-danger" id="confirmCancel">ยืนยันการยกเลิก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        let bookingToCancel = null;
        
        function cancelBooking(bookingId) {
            bookingToCancel = bookingId;
            const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
            modal.show();
        }
        
        document.getElementById('confirmCancel').addEventListener('click', function() {
            if (bookingToCancel) {
                // ส่ง AJAX request เพื่อยกเลิกการจอง
                fetch('ajax/cancel_booking.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `booking_id=${bookingToCancel}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการยกเลิกการจอง');
                });
                
                // ปิด modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('cancelModal'));
                modal.hide();
            }
        });
    </script>
</body>
</html>
