<?php
/**
 * หน้าจัดการบริการ - ใช้โค้ดจากหน้าที่ทำงานได้
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ดึงรายการบริการ
$services = fetchAll("SELECT * FROM services ORDER BY id DESC");



// ประมวลผลฟอร์มแก้ไข (คัดลอกจาก test_edit_service_debug.php)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit_service') {
    $service_id = intval($_POST['service_id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $duration = intval($_POST['duration'] ?? 0);
    
    // ตรวจสอบข้อมูลพื้นฐาน
    if ($service_id <= 0) {
        $error_message = 'ไม่พบบริการที่ต้องการแก้ไข';
    } elseif (empty($name)) {
        $error_message = 'กรุณากรอกชื่อบริการ';
    } elseif ($price <= 0) {
        $error_message = 'กรุณากรอกราคาที่ถูกต้อง';
    } elseif ($duration <= 0) {
        $error_message = 'กรุณากรอกระยะเวลาที่ถูกต้อง';
    } else {
        // ดึงข้อมูลบริการเดิม
        $service = fetchOne("SELECT * FROM services WHERE id = ?", [$service_id]);
        
        if ($service) {
            $image_filename = $service['image']; // ใช้รูปเดิม
            
            // ตรวจสอบการอัปโหลดรูปภาพใหม่
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $max_size = 5 * 1024 * 1024; // 5MB
                
                if (!in_array($file['type'], $allowed_types)) {
                    $error_message = 'รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น';
                } elseif ($file['size'] > $max_size) {
                    $error_message = 'ขนาดไฟล์ต้องไม่เกิน 5MB';
                } else {
                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $new_image_filename = 'service_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
                    $upload_dir = __DIR__ . '/../assets/img/services/';
                    $upload_path = $upload_dir . $new_image_filename;
                    
                    // สร้างโฟลเดอร์ถ้ายังไม่มี
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                        // ลบรูปเดิม
                        if ($image_filename && file_exists($upload_dir . $image_filename)) {
                            unlink($upload_dir . $image_filename);
                        }
                        $image_filename = $new_image_filename;
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์';
                    }
                }
            }
            
            // บันทึกลงฐานข้อมูล
            if (empty($error_message)) {
                try {
                    $sql = "UPDATE services SET name = ?, description = ?, price = ?, duration = ?, image = ?, updated_at = NOW() WHERE id = ?";
                    $result = executeQuery($sql, [$name, $description, $price, $duration, $image_filename, $service_id]);
                    
                    if ($result) {
                        $success_message = 'แก้ไขบริการเรียบร้อยแล้ว';
                        if ($image_filename) {
                            $success_message .= ' (รูปภาพ: ' . $image_filename . ')';
                        }
                        
                        // อัปเดตข้อมูลสำหรับแสดงในฟอร์ม
                        $selected_service = fetchOne("SELECT * FROM services WHERE id = ?", [$service_id]);
                        
                        // รีเฟรชรายการบริการ
                        $services = fetchAll("SELECT * FROM services ORDER BY id DESC");
                        
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการอัปเดทข้อมูล';
                    }
                } catch (Exception $e) {
                    $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
                }
            }
        } else {
            $error_message = 'ไม่พบบริการที่ต้องการแก้ไข';
        }
    }
}

// ประมวลผลฟอร์มเพิ่มบริการ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_service') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $duration = intval($_POST['duration'] ?? 0);
    
    // ตรวจสอบข้อมูลพื้นฐาน
    if (empty($name)) {
        $error_message = 'กรุณากรอกชื่อบริการ';
    } elseif ($price <= 0) {
        $error_message = 'กรุณากรอกราคาที่ถูกต้อง';
    } elseif ($duration <= 0) {
        $error_message = 'กรุณากรอกระยะเวลาที่ถูกต้อง';
    } else {
        $image_filename = null;
        
        // ตรวจสอบการอัปโหลดรูปภาพ
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['image'];
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (!in_array($file['type'], $allowed_types)) {
                $error_message = 'รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น';
            } elseif ($file['size'] > $max_size) {
                $error_message = 'ขนาดไฟล์ต้องไม่เกิน 5MB';
            } else {
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $image_filename = 'service_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
                $upload_dir = __DIR__ . '/../assets/img/services/';
                $upload_path = $upload_dir . $image_filename;
                
                // สร้างโฟลเดอร์ถ้ายังไม่มี
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
                    $error_message = 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์';
                    $image_filename = null;
                }
            }
        }
        
        // บันทึกลงฐานข้อมูล
        if (empty($error_message)) {
            try {
                $sql = "INSERT INTO services (name, description, price, duration, image, is_active, created_at) VALUES (?, ?, ?, ?, ?, 1, NOW())";
                $result = executeQuery($sql, [$name, $description, $price, $duration, $image_filename]);
                
                if ($result) {
                    $success_message = 'เพิ่มบริการเรียบร้อยแล้ว';
                    if ($image_filename) {
                        $success_message .= ' (รูปภาพ: ' . $image_filename . ')';
                    }
                    
                    // รีเฟรชรายการบริการ
                    $services = fetchAll("SELECT * FROM services ORDER BY id DESC");
                    
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการเพิ่มข้อมูล';
                }
            } catch (Exception $e) {
                $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            }
        }
    }
}

// ประมวลผลการลบบริการ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_service') {
    $service_id = intval($_POST['service_id'] ?? 0);
    
    if ($service_id > 0) {
        // ดึงข้อมูลบริการเพื่อลบรูปภาพ
        $service = fetchOne("SELECT * FROM services WHERE id = ?", [$service_id]);
        
        if ($service) {
            try {
                $result = executeQuery("DELETE FROM services WHERE id = ?", [$service_id]);
                
                if ($result) {
                    // ลบรูปภาพ
                    if ($service['image']) {
                        $image_path = __DIR__ . '/../assets/img/services/' . $service['image'];
                        if (file_exists($image_path)) {
                            unlink($image_path);
                        }
                    }
                    
                    $success_message = 'ลบบริการเรียบร้อยแล้ว';
                    
                    // รีเฟรชรายการบริการ
                    $services = fetchAll("SELECT * FROM services ORDER BY id DESC");
                    
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการลบข้อมูล';
                }
            } catch (Exception $e) {
                $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            }
        } else {
            $error_message = 'ไม่พบบริการที่ต้องการลบ';
        }
    } else {
        $error_message = 'ข้อมูลไม่ถูกต้อง';
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการบริการ - ระบบจองคิวร้านตัดผม</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        body {
            padding-top: 76px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            background-attachment: fixed;
        }

        .admin-header {
            background: var(--gradient-dark);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 25px 25px;
            box-shadow: var(--shadow-medium);
        }

        .service-card {
            transition: var(--transition);
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-soft);
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-strong);
        }

        .service-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: var(--transition);
        }

        .service-card:hover .service-image {
            transform: scale(1.05);
        }

        .current-image {
            max-width: 250px;
            max-height: 250px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
        }

        .btn-modern {
            border-radius: 25px;
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .modal-content {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-strong);
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(233, 69, 96, 0.25);
        }

        .stats-card {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: var(--transition);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> แดชบอร์ด
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_services.php">
                            <i class="fas fa-cogs"></i> จัดการบริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_bookings.php">
                            <i class="fas fa-calendar-alt"></i> จัดการการจอง
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php">
                            <i class="fas fa-users"></i> จัดการผู้ใช้
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar"></i> รายงาน
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> <?= sanitize($_SESSION['full_name'] ?? 'Admin') ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/index.php">
                                <i class="fas fa-home"></i> หน้าแรก
                            </a></li>
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit"></i> แก้ไขโปรไฟล์
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">
                                <i class="fas fa-sign-out-alt"></i> ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title mb-2">
                        <i class="fas fa-cogs me-3"></i>จัดการบริการ
                    </h1>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-info-circle me-2"></i>
                        จัดการบริการของร้าน เพิ่ม แก้ไข หรือลบบริการต่างๆ
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-light btn-modern btn-lg" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-0"><?= count($services) ?></h3>
                            <p class="mb-0 opacity-75">บริการทั้งหมด</p>
                        </div>
                        <i class="fas fa-cut fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: var(--gradient-secondary);">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-0"><?= count(array_filter($services, function($s) { return $s['is_active']; })) ?></h3>
                            <p class="mb-0 opacity-75">บริการที่เปิดใช้งาน</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: var(--gradient-gold);">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-0"><?= count(array_filter($services, function($s) { return !empty($s['image']); })) ?></h3>
                            <p class="mb-0 opacity-75">บริการที่มีรูปภาพ</p>
                        </div>
                        <i class="fas fa-image fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="mb-0"><?= !empty($services) ? number_format(array_sum(array_column($services, 'price')) / count($services)) : 0 ?></h3>
                            <p class="mb-0 opacity-75">ราคาเฉลี่ย (บาท)</p>
                        </div>
                        <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">

                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error_message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <?php if (empty($services)): ?>
                        <div class="col-12">
                            <div class="empty-state">
                                <div class="mb-4">
                                    <i class="fas fa-cut fa-4x text-muted mb-3"></i>
                                    <h3 class="text-muted">ยังไม่มีบริการ</h3>
                                    <p class="text-muted mb-4">เริ่มต้นสร้างบริการแรกของร้านคุณ เพื่อให้ลูกค้าสามารถจองได้</p>
                                    <button type="button" class="btn btn-primary btn-modern btn-lg" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                                        <i class="fas fa-plus me-2"></i>เพิ่มบริการแรก
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($services as $service): ?>
                            <div class="col-md-6 col-xl-4 mb-4">
                                <div class="card service-card h-100">
                                    <div class="position-relative">
                                        <?php if ($service['image']): ?>
                                            <img src="../serve_image.php?image=<?= urlencode($service['image']) ?>"
                                                 class="service-image"
                                                 alt="<?= htmlspecialchars($service['name']) ?>">
                                        <?php else: ?>
                                            <div class="service-image bg-gradient-to-br from-gray-100 to-gray-200 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-cut fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Status Badge -->
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <?php if ($service['is_active']): ?>
                                                <span class="badge bg-success">เปิดใช้งาน</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">ปิดใช้งาน</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="card-body">
                                        <h5 class="card-title fw-bold text-dark"><?= htmlspecialchars($service['name']) ?></h5>
                                        <?php if ($service['description']): ?>
                                            <p class="card-text text-muted small"><?= htmlspecialchars(substr($service['description'], 0, 100)) ?><?= strlen($service['description']) > 100 ? '...' : '' ?></p>
                                        <?php endif; ?>

                                        <div class="row g-2 mb-3">
                                            <div class="col-6">
                                                <div class="text-center p-2 bg-light rounded">
                                                    <div class="fw-bold text-primary"><?= number_format($service['price']) ?></div>
                                                    <small class="text-muted">บาท</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center p-2 bg-light rounded">
                                                    <div class="fw-bold text-info"><?= $service['duration'] ?></div>
                                                    <small class="text-muted">นาที</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-outline-primary btn-modern"
                                                        onclick="editService(<?= $service['id'] ?>)">
                                                    <i class="fas fa-edit me-1"></i>แก้ไข
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-modern"
                                                        onclick="deleteService(<?= $service['id'] ?>, '<?= htmlspecialchars($service['name']) ?>')">
                                                    <i class="fas fa-trash me-1"></i>ลบ
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card-footer bg-transparent border-0">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            อัปเดต: <?= date('d/m/Y H:i', strtotime($service['updated_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Modal เพิ่มบริการ -->
    <div class="modal fade" id="addServiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>เพิ่มบริการใหม่
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body p-4">
                        <input type="hidden" name="action" value="add_service">

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="addServiceName" class="form-label fw-bold">
                                        <i class="fas fa-tag me-1"></i>ชื่อบริการ <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="addServiceName" name="name"
                                           placeholder="เช่น ตัดผมชาย, สระผม, ย้อมสี" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="addServicePrice" class="form-label fw-bold">
                                        <i class="fas fa-money-bill-wave me-1"></i>ราคา (บาท) <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-lg" id="addServicePrice" name="price"
                                               min="1" step="0.01" placeholder="299" required>
                                        <span class="input-group-text">บาท</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="addServiceDescription" class="form-label fw-bold">
                                <i class="fas fa-align-left me-1"></i>คำอธิบายบริการ
                            </label>
                            <textarea class="form-control" id="addServiceDescription" name="description" rows="3"
                                      placeholder="อธิบายรายละเอียดของบริการ เช่น ขั้นตอนการทำ อุปกรณ์ที่ใช้"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addServiceDuration" class="form-label fw-bold">
                                        <i class="fas fa-clock me-1"></i>ระยะเวลา <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-lg" id="addServiceDuration" name="duration"
                                               min="1" placeholder="60" required>
                                        <span class="input-group-text">นาที</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="addServiceImage" class="form-label fw-bold">
                                        <i class="fas fa-image me-1"></i>รูปภาพบริการ
                                    </label>
                                    <input type="file" class="form-control form-control-lg" id="addServiceImage" name="image" accept="image/*">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        รองรับ JPG, PNG, GIF ขนาดไม่เกิน 5MB
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Area -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-eye me-1"></i>ตัวอย่างการแสดงผล
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-img-top bg-secondary d-flex align-items-center justify-content-center" style="height: 120px;">
                                            <i class="fas fa-image fa-2x text-white"></i>
                                        </div>
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1" id="previewName">ชื่อบริการ</h6>
                                            <div class="d-flex justify-content-between">
                                                <span class="badge bg-primary" id="previewPrice">0 บาท</span>
                                                <small class="text-muted" id="previewDuration">0 นาที</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>ยกเลิก
                        </button>
                        <button type="submit" class="btn btn-primary btn-modern">
                            <i class="fas fa-save me-1"></i>บันทึกบริการ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal แก้ไขบริการ -->
    <div class="modal fade" id="editServiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>แก้ไขบริการ
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="editServiceForm">
                    <div class="modal-body p-4">
                        <input type="hidden" name="action" value="edit_service">
                        <input type="hidden" name="service_id" id="editServiceId">

                        <!-- Current Image Display -->
                        <div class="mb-3" id="currentImageSection" style="display: none;">
                            <label class="form-label fw-bold">
                                <i class="fas fa-image me-1"></i>รูปภาพปัจจุบัน
                            </label>
                            <div class="text-center">
                                <img id="currentImage" src="" alt="Current Image" class="current-image mb-2">
                                <br><small class="text-muted" id="currentImageName"></small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="editServiceName" class="form-label fw-bold">
                                        <i class="fas fa-tag me-1"></i>ชื่อบริการ <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="editServiceName" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editServicePrice" class="form-label fw-bold">
                                        <i class="fas fa-money-bill-wave me-1"></i>ราคา (บาท) <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-lg" id="editServicePrice" name="price"
                                               min="1" step="0.01" required>
                                        <span class="input-group-text">บาท</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editServiceDescription" class="form-label fw-bold">
                                <i class="fas fa-align-left me-1"></i>คำอธิบายบริการ
                            </label>
                            <textarea class="form-control" id="editServiceDescription" name="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editServiceDuration" class="form-label fw-bold">
                                        <i class="fas fa-clock me-1"></i>ระยะเวลา <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-lg" id="editServiceDuration" name="duration"
                                               min="1" required>
                                        <span class="input-group-text">นาที</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editServiceImage" class="form-label fw-bold">
                                        <i class="fas fa-image me-1"></i>รูปภาพใหม่ (ถ้าต้องการเปลี่ยน)
                                    </label>
                                    <input type="file" class="form-control form-control-lg" id="editServiceImage" name="image" accept="image/*">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        รองรับ JPG, PNG, GIF ขนาดไม่เกิน 5MB
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Area -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-eye me-1"></i>ตัวอย่างการแสดงผลหลังแก้ไข
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-img-top bg-secondary d-flex align-items-center justify-content-center" style="height: 120px;">
                                            <i class="fas fa-image fa-2x text-white" id="editPreviewIcon"></i>
                                            <img id="editPreviewImage" src="" alt="Preview" style="width: 100%; height: 100%; object-fit: cover; display: none;">
                                        </div>
                                        <div class="card-body p-2">
                                            <h6 class="card-title mb-1" id="editPreviewName">ชื่อบริการ</h6>
                                            <div class="d-flex justify-content-between">
                                                <span class="badge bg-primary" id="editPreviewPrice">0 บาท</span>
                                                <small class="text-muted" id="editPreviewDuration">0 นาที</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>ยกเลิก
                        </button>
                        <button type="submit" class="btn btn-warning btn-modern">
                            <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>



    <!-- Form สำหรับลบบริการ -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_service">
        <input type="hidden" name="service_id" id="deleteServiceId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // ฟังก์ชันลบบริการ
        function deleteService(serviceId, serviceName) {
            Swal.fire({
                title: 'ยืนยันการลบ',
                text: `คุณต้องการลบบริการ "${serviceName}" หรือไม่?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ลบ',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('deleteServiceId').value = serviceId;
                    document.getElementById('deleteForm').submit();
                }
            });
        }

        // ฟังก์ชันแก้ไขบริการ
        function editService(serviceId) {
            // แสดง loading
            Swal.fire({
                title: 'กำลังโหลดข้อมูล...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // ดึงข้อมูลบริการ
            fetch(`get_service.php?id=${serviceId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.text().then(text => {
                        console.log('Raw response:', text);
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('JSON parse error:', e);
                            console.error('Response text:', text);
                            throw new Error('Invalid JSON response');
                        }
                    });
                })
                .then(data => {
                    Swal.close();
                    console.log('Parsed data:', data);

                    if (data.success) {
                        const service = data.service;

                        // เติมข้อมูลในฟอร์ม
                        document.getElementById('editServiceId').value = service.id;
                        document.getElementById('editServiceName').value = service.name;
                        document.getElementById('editServiceDescription').value = service.description || '';
                        document.getElementById('editServicePrice').value = service.price;
                        document.getElementById('editServiceDuration').value = service.duration;

                        // แสดงรูปภาพปัจจุบัน
                        if (service.image) {
                            document.getElementById('currentImageSection').style.display = 'block';
                            document.getElementById('currentImage').src = `../serve_image.php?image=${encodeURIComponent(service.image)}`;
                            document.getElementById('currentImageName').textContent = service.image;

                            // แสดงในพรีวิว
                            document.getElementById('editPreviewImage').src = `../serve_image.php?image=${encodeURIComponent(service.image)}`;
                            document.getElementById('editPreviewImage').style.display = 'block';
                            document.getElementById('editPreviewIcon').style.display = 'none';
                        } else {
                            document.getElementById('currentImageSection').style.display = 'none';
                            document.getElementById('editPreviewImage').style.display = 'none';
                            document.getElementById('editPreviewIcon').style.display = 'block';
                        }

                        // อัปเดตพรีวิว
                        updateEditPreview();

                        // แสดง Modal
                        const modal = new bootstrap.Modal(document.getElementById('editServiceModal'));
                        modal.show();

                    } else {
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด!',
                            text: data.message || 'ไม่สามารถโหลดข้อมูลบริการได้',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    Swal.close();
                    console.error('Fetch error:', error);
                    Swal.fire({
                        title: 'เกิดข้อผิดพลาด!',
                        text: `ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้: ${error.message}`,
                        icon: 'error'
                    });
                });
        }

        // Auto hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Preview functionality for add service modal
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('addServiceName');
            const priceInput = document.getElementById('addServicePrice');
            const durationInput = document.getElementById('addServiceDuration');

            const previewName = document.getElementById('previewName');
            const previewPrice = document.getElementById('previewPrice');
            const previewDuration = document.getElementById('previewDuration');

            function updatePreview() {
                previewName.textContent = nameInput.value || 'ชื่อบริการ';
                previewPrice.textContent = (priceInput.value ? number_format(priceInput.value) : '0') + ' บาท';
                previewDuration.textContent = (durationInput.value || '0') + ' นาที';
            }

            nameInput.addEventListener('input', updatePreview);
            priceInput.addEventListener('input', updatePreview);
            durationInput.addEventListener('input', updatePreview);

            // Preview functionality for edit service modal
            const editNameInput = document.getElementById('editServiceName');
            const editPriceInput = document.getElementById('editServicePrice');
            const editDurationInput = document.getElementById('editServiceDuration');
            const editImageInput = document.getElementById('editServiceImage');

            const editPreviewName = document.getElementById('editPreviewName');
            const editPreviewPrice = document.getElementById('editPreviewPrice');
            const editPreviewDuration = document.getElementById('editPreviewDuration');
            const editPreviewImage = document.getElementById('editPreviewImage');
            const editPreviewIcon = document.getElementById('editPreviewIcon');

            function updateEditPreview() {
                editPreviewName.textContent = editNameInput.value || 'ชื่อบริการ';
                editPreviewPrice.textContent = (editPriceInput.value ? number_format(editPriceInput.value) : '0') + ' บาท';
                editPreviewDuration.textContent = (editDurationInput.value || '0') + ' นาที';
            }

            editNameInput.addEventListener('input', updateEditPreview);
            editPriceInput.addEventListener('input', updateEditPreview);
            editDurationInput.addEventListener('input', updateEditPreview);

            // Handle file input for edit modal
            editImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        editPreviewImage.src = e.target.result;
                        editPreviewImage.style.display = 'block';
                        editPreviewIcon.style.display = 'none';
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Number formatting function
            function number_format(number) {
                return new Intl.NumberFormat('th-TH').format(number);
            }

            // Smooth animations
            const cards = document.querySelectorAll('.service-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate__animated', 'animate__fadeInUp');
            });

            // Success message animation
            <?php if ($success_message): ?>
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: '<?= addslashes($success_message) ?>',
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                });
            <?php endif; ?>

            <?php if ($error_message): ?>
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: '<?= addslashes($error_message) ?>',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            <?php endif; ?>
        });
    </script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        .animate__fadeInUp {
            animation-duration: 0.6s;
        }
    </style>
</body>
</html>
