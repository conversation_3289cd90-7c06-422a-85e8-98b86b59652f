<?php
require_once __DIR__ . '/../includes/functions.php';

// ดึงข้อมูลโปรโมชั่นที่ยังใช้งานได้
$promotions = fetchAll("
    SELECT * FROM promotions 
    WHERE is_active = 1 
    AND start_date <= CURDATE() 
    AND end_date >= CURDATE() 
    ORDER BY created_at DESC
");

// ดึงข้อมูลโปรโมชั่นที่จะมาถึง
$upcoming_promotions = fetchAll("
    SELECT * FROM promotions 
    WHERE is_active = 1 
    AND start_date > CURDATE() 
    ORDER BY start_date ASC
");
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>โปรโมชั่น - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="promotion.php">โปรโมชั่น</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">แกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">เกี่ยวกับเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว</a></li>
                                    <li><a class="dropdown-item" href="../customer/my_bookings.php">การจองของฉัน</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 bg-warning text-dark" style="margin-top: 76px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-tags"></i> โปรโมชั่นพิเศษ
                    </h1>
                    <p class="lead">ข้อเสนอพิเศษและส่วนลดสำหรับลูกค้าของเรา</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Current Promotions -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold">โปรโมชั่นปัจจุบัน</h2>
                    <p class="lead text-muted">ข้อเสนอพิเศษที่ใช้ได้ในขณะนี้</p>
                </div>
            </div>

            <?php if (empty($promotions)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <h4>ไม่มีโปรโมชั่นในขณะนี้</h4>
                            <p>กรุณาติดตามโปรโมชั่นใหม่ๆ ของเราในอนาคต</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($promotions as $promotion): ?>
                        <div class="col-lg-6 col-md-6">
                            <div class="card promotion-card h-100">
                                <?php if ($promotion['image']): ?>
                                    <img src="../assets/img/promotion/<?= sanitize($promotion['image']) ?>"
                                         class="card-img-top"
                                         alt="<?= sanitize($promotion['title']) ?>"
                                         style="height: 200px; object-fit: cover;">
                                <?php endif; ?>
                                
                                <div class="card-body text-white">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h5 class="card-title"><?= sanitize($promotion['title']) ?></h5>
                                        <?php if ($promotion['discount_percent']): ?>
                                            <span class="badge bg-light text-dark fs-6">
                                                ลด <?= $promotion['discount_percent'] ?>%
                                            </span>
                                        <?php elseif ($promotion['discount_amount']): ?>
                                            <span class="badge bg-light text-dark fs-6">
                                                ลด <?= number_format($promotion['discount_amount']) ?> บาท
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <p class="card-text"><?= nl2br(sanitize($promotion['description'])) ?></p>
                                    
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small>
                                                <i class="fas fa-calendar"></i> 
                                                ถึง <?= formatDateThai($promotion['end_date']) ?>
                                            </small>
                                            
                                            <?php if (isLoggedIn() && !isAdmin()): ?>
                                                <a href="../customer/book.php" class="btn btn-light">
                                                    <i class="fas fa-calendar-plus"></i> ใช้โปรโมชั่น
                                                </a>
                                            <?php elseif (!isLoggedIn()): ?>
                                                <a href="login.php" class="btn btn-light">
                                                    <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Upcoming Promotions -->
    <?php if (!empty($upcoming_promotions)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold">โปรโมชั่นที่จะมาถึง</h2>
                    <p class="lead text-muted">เตรียมพบกับข้อเสนอพิเศษในอนาคต</p>
                </div>
            </div>

            <div class="row g-4">
                <?php foreach ($upcoming_promotions as $promotion): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <?php if ($promotion['image']): ?>
                                <img src="../assets/img/promotion/<?= sanitize($promotion['image']) ?>"
                                     class="card-img-top"
                                     alt="<?= sanitize($promotion['title']) ?>"
                                     style="height: 200px; object-fit: cover;">
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title"><?= sanitize($promotion['title']) ?></h5>
                                    <?php if ($promotion['discount_percent']): ?>
                                        <span class="badge bg-warning text-dark">
                                            ลด <?= $promotion['discount_percent'] ?>%
                                        </span>
                                    <?php elseif ($promotion['discount_amount']): ?>
                                        <span class="badge bg-warning text-dark">
                                            ลด <?= number_format($promotion['discount_amount']) ?> บาท
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <p class="card-text text-muted"><?= nl2br(sanitize($promotion['description'])) ?></p>
                                
                                <div class="mt-auto">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt"></i> 
                                        เริ่ม <?= formatDateThai($promotion['start_date']) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- How to Use Promotions -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="mb-4">
                                <i class="fas fa-question-circle text-primary"></i> 
                                วิธีใช้โปรโมชั่น
                            </h3>
                            
                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="step-item">
                                        <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                            1
                                        </div>
                                        <h5>เข้าสู่ระบบ</h5>
                                        <p class="text-muted">เข้าสู่ระบบหรือสมัครสมาชิกใหม่</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="step-item">
                                        <div class="step-number bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                            2
                                        </div>
                                        <h5>จองคิว</h5>
                                        <p class="text-muted">เลือกบริการและวันเวลาที่ต้องการ</p>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="step-item">
                                        <div class="step-number bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                            3
                                        </div>
                                        <h5>เลือกโปรโมชั่น</h5>
                                        <p class="text-muted">เลือกโปรโมชั่นที่ต้องการใช้</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <?php if (isLoggedIn() && !isAdmin()): ?>
                                    <a href="../customer/book.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calendar-plus"></i> จองคิวเลย
                                    </a>
                                <?php elseif (!isLoggedIn()): ?>
                                    <a href="register.php" class="btn btn-primary btn-lg me-3">
                                        <i class="fas fa-user-plus"></i> สมัครสมาชิก
                                    </a>
                                    <a href="login.php" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4">
                        <i class="fas fa-bell"></i> รับข่าวสารโปรโมชั่น
                    </h3>
                    <p class="lead mb-4">
                        สมัครรับข่าวสารเพื่อไม่พลาดโปรโมชั่นพิเศษและข้อเสนอใหม่ๆ
                    </p>
                    
                    <form class="row g-3 justify-content-center">
                        <div class="col-md-6">
                            <input type="email" class="form-control form-control-lg" placeholder="อีเมลของคุณ">
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-paper-plane"></i> สมัครรับข่าวสาร
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-cut"></i> Barber Shop</h5>
                    <p>ร้านตัดผมมืออาชีพ ให้บริการด้วยใจ มีคุณภาพ</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                </div>
                <div class="col-md-4">
                    <h5>เวลาทำการ</h5>
                    <p>จันทร์ - เสาร์: 09:00 - 20:00</p>
                    <p>อาทิตย์: 10:00 - 18:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Barber Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>

    <style>
        .step-number {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .promotion-card {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border: none;
            overflow: hidden;
            position: relative;
        }
        
        .promotion-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transform: rotate(45deg);
            z-index: 1;
        }
        
        .promotion-card .card-body {
            position: relative;
            z-index: 2;
        }
    </style>
</body>
</html>
