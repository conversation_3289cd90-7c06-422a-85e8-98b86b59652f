/* ===== MODERN BARBER SHOP STYLES ===== */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* ===== SERVICE CARD STYLES ===== */
.service-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20px !important;
    background: #ffffff;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

.service-card .card-img-top {
    border-radius: 20px 20px 0 0 !important;
    position: relative;
    overflow: hidden;
}

.service-card .card-body {
    border-radius: 0 0 20px 20px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
}

.service-card .badge {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
    color: #667eea !important;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Service Image Hover Effect */
.service-card img {
    transition: all 0.4s ease;
}

.service-card:hover img {
    transform: scale(1.1);
}

/* Service Button Hover Effects */
.service-card .btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.service-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* ===== SERVICE IMAGE ONLY STYLES ===== */
.service-image-only {
    cursor: pointer;
    border-radius: 20px !important;
}

.service-image-only:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
}

.service-image-only:hover .service-hover-overlay {
    opacity: 1 !important;
}

.service-hover-overlay {
    border-radius: 20px;
}

.service-hover-overlay .btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.service-hover-overlay .btn:hover {
    transform: translateY(-2px) scale(1.05);
}

:root {
    /* Modern Color Palette */
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #e94560;
    --gold-color: #ffd700;
    --dark-color: #0f0f23;
    --light-color: #f8f9fa;
    --text-light: #ffffff;
    --text-dark: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-gold: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 35px rgba(0, 0, 0, 0.15);
    --shadow-strong: 0 20px 40px rgba(0, 0, 0, 0.2);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Base Styles */
* {
    font-family: 'Poppins', sans-serif;
    box-sizing: border-box;
}

/* ปรับปรุงความคมชัดของข้อความ */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

p, span, div {
    color: #34495e;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* ข้อความบน Hero Section */
.hero-content h1 {
    color: #ffffff !important;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.hero-content p {
    color: #ecf0f1 !important;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

html {
    scroll-behavior: smooth;
}

body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    background-attachment: fixed;
    color: #2c3e50;
    line-height: 1.7;
    overflow-x: hidden;
    font-weight: 400;
}

/* Modern Container */
.container {
    position: relative;
    z-index: 1;
}

/* Glass Morphism Effect - สบายตา */
.glass-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

/* Modern Card Styles - สบายตา */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    color: #2c3e50;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

.card-header {
    background: var(--gradient-primary);
    color: var(--text-light);
    border: none;
    padding: 1.5rem;
    font-weight: 600;
}

/* Modern Buttons */
.btn {
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    color: #ffffff;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
    color: #ffffff;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    color: #ffffff;
}

/* ไฟล์ CSS หลักสำหรับระบบจองคิวร้านตัดผม */

/* ===== MODERN NAVIGATION ===== */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #2c3e50 !important;
    text-decoration: none;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
    margin: 0 10px;
    padding: 10px 20px !important;
    border-radius: 25px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* ===== MODERN HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #3498db 100%);
    color: #ffffff;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--text-light);
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease-out 0.4s both;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Admin Dashboard Styles */
.bg-purple {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
}

.text-purple {
    color: #6f42c1;
}

/* ===== MODERN FORMS ===== */
.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-control {
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(233, 69, 96, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.form-select {
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(233, 69, 96, 0.25);
}

/* ===== MODERN GALLERY ===== */
.gallery-thumb {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
}

.gallery-thumb:hover {
    transform: scale(1.05) rotate(1deg);
    box-shadow: var(--shadow-strong);
}

.gallery-thumb img {
    transition: var(--transition);
}

.gallery-thumb:hover img {
    transform: scale(1.1);
}

.gallery-thumb-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px;
    font-size: 10px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-thumb:hover .gallery-thumb-overlay {
    opacity: 1;
}

/* Global Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Feature Icons */
.feature-icon {
    width: 80px;
    height: 80px;
    font-size: 2rem;
}

/* Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Login/Register Pages */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--light-color) 0%, #bdc3c7 100%);
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px;
    text-align: center;
}

.auth-body {
    padding: 40px;
}

/* Dashboard */
.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 25px;
    margin-bottom: 25px;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
}

.stat-card {
    text-align: center;
    padding: 30px 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Status Badges */
.status-pending {
    background-color: var(--warning-color);
    color: white;
}

.status-confirmed {
    background-color: var(--success-color);
    color: white;
}

.status-completed {
    background-color: var(--secondary-color);
    color: white;
}

.status-cancelled {
    background-color: var(--danger-color);
    color: white;
}

/* Booking Form */
.booking-form {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 30px;
}

.time-slot {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
    background: #f8f9fa;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.time-slot:hover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.time-slot.active,
.time-slot.selected {
    border-color: var(--secondary-color);
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.time-slot.unavailable {
    background-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.time-slot.unavailable:hover {
    transform: none;
    box-shadow: none;
    border-color: #e9ecef;
    background-color: #e9ecef;
}

.time-slot strong {
    font-size: 1.1em;
    font-weight: 600;
}

.time-slot small {
    font-size: 0.8em;
    opacity: 0.8;
    margin-top: 2px;
}

/* Gallery */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 20px;
}

.gallery-item img {
    transition: transform 0.3s ease;
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Promotion Cards */
.promotion-card {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    color: white;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.promotion-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transform: rotate(45deg);
}

/* Footer */
footer {
    background: var(--dark-color) !important;
}

footer h5 {
    color: var(--secondary-color);
    margin-bottom: 20px;
}

footer p {
    margin-bottom: 10px;
}

footer i {
    margin-right: 10px;
    color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding-top: 100px;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .auth-body {
        padding: 30px 20px;
    }
    
    .dashboard-card {
        margin-bottom: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border-left: 4px solid var(--secondary-color);
}

/* ===== RESPONSIVE DESIGN IMPROVEMENTS ===== */

/* Mobile First Approach */
@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card {
        margin-bottom: 15px;
        border-radius: 8px;
    }

    .btn {
        font-size: 14px;
        padding: 8px 12px;
        margin-bottom: 5px;
    }

    .btn-group .btn {
        margin-bottom: 0;
    }

    .table-responsive {
        font-size: 12px;
    }

    .table th, .table td {
        padding: 8px 4px;
    }

    .navbar-brand {
        font-size: 18px;
    }

    .navbar-nav .nav-link {
        padding: 8px 12px;
    }

    .display-4 {
        font-size: 2rem;
    }

    .hero-section h1 {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .hero-section p {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .form-floating {
        margin-bottom: 15px;
    }

    .modal-dialog {
        margin: 10px;
    }

    .floating-btn {
        bottom: 15px;
        right: 15px;
        padding: 12px 20px;
    }

    .gallery-thumb {
        margin-bottom: 15px;
    }

    .stats-card {
        margin-bottom: 15px;
    }

    .timeline-item {
        padding-left: 30px;
    }

    .booking-receipt {
        padding: 10px;
        font-size: 12px;
    }
}

/* Tablet */
@media (min-width: 577px) and (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .card-columns {
        column-count: 2;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .btn {
        font-size: 15px;
        padding: 10px 16px;
    }

    .table-responsive {
        font-size: 13px;
    }

    .navbar-nav .nav-link {
        padding: 10px 15px;
    }
}

/* Desktop */
@media (min-width: 769px) {
    .card-columns {
        column-count: 3;
    }

    .hero-section {
        padding: 80px 0;
    }

    .stats-section {
        padding: 60px 0;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    .card-columns {
        column-count: 4;
    }
}

/* Print Styles */
@media print {
    .no-print, .navbar, .footer, .btn, .alert, .floating-btn {
        display: none !important;
    }

    .container {
        width: 100% !important;
        max-width: none !important;
        padding: 0 !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .page-break {
        page-break-before: always;
    }
}

/* ===== MODERN LOADING SPINNER ===== */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== MODERN ALERTS - สบายตา ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    border-left: 4px solid;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-left-color: #27ae60;
    color: #1e7e34;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-left-color: #e74c3c;
    color: #a93226;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-left-color: #f39c12;
    color: #b7950b;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-left-color: #3498db;
    color: #1f5f8b;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== MODERN TABLES - สบายตา ===== */
.table {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    color: #2c3e50;
}

.table thead th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: #ffffff;
    border: none;
    padding: 1rem;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.table tbody tr {
    transition: var(--transition);
    color: #2c3e50;
}

.table tbody tr:hover {
    background: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

.table tbody td {
    color: #34495e;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* ===== MODERN BADGES ===== */
.badge {
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== FLOATING ELEMENTS ===== */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== MODERN SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* ===== MODERN FOOTER ===== */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ffffff;
    padding: 3rem 0 1rem;
    margin-top: 5rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(135deg, #3498db, #27ae60);
}

/* ===== MODERN STATS CARDS ===== */
.stats-card {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: var(--transition);
}

.stats-card:hover::before {
    transform: scale(1);
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: var(--shadow-strong);
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
