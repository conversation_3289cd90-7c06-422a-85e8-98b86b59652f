<?php
require_once __DIR__ . '/../includes/functions.php';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ร้านตัดผม Barber Shop - หน้าแรก</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="promotion.php">โปรโมชั่น</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">แกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">เกี่ยวกับเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว</a></li>
                                    <li><a class="dropdown-item" href="../customer/my_bookings.php">การจองของฉัน</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Modern Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">MODERN<br>BARBER SHOP</h1>
                        <p class="hero-subtitle">
                            บริการตัดผมคุณภาพสูง ด้วยช่างมืออาชีพ และเครื่องมือที่ทันสมัย<br>
                            พร้อมระบบจองคิวออนไลน์ที่สะดวกรวดเร็ว
                        </p>
                        <div class="hero-buttons d-flex gap-3 justify-content-center justify-content-lg-start">
                            <?php if (isLoggedIn() && !isAdmin()): ?>
                                <a href="../customer/book.php" class="btn btn-success btn-lg pulse-animation">
                                    <i class="fas fa-calendar-plus"></i> จองคิวเลย
                                </a>
                            <?php else: ?>
                                <a href="register.php" class="btn btn-success btn-lg pulse-animation">
                                    <i class="fas fa-user-plus"></i> สมัครสมาชิก
                                </a>
                            <?php endif; ?>
                            <a href="services.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-cut"></i> ดูบริการ
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="floating-element">
                        <?php
                        $image_path = '../assets/img/me.svg';
                        $fallback_path = '../assets/img/logo-barber.png';
                        $final_path = file_exists($image_path) ? $image_path : $fallback_path;
                        ?>
                        <img src="<?= $final_path ?>" alt="Modern Barber Shop" class="img-fluid" style="max-height: 500px; filter: drop-shadow(0 20px 40px rgba(0,0,0,0.3)); border-radius: 20px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator text-center" style="position: absolute; bottom: 30px; left: 50%; transform: translateX(-50%);">
            <div class="scroll-arrow text-white" style="animation: bounce 2s infinite;">
                <i class="fas fa-chevron-down fa-2x"></i>
            </div>
        </div>
    </section>

    <!-- Modern Features Section -->
    <section class="py-5" style="background: rgba(255,255,255,0.9); backdrop-filter: blur(10px);">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-12">
                    <h2 class="display-5 fw-bold mb-4" style="color: #2c3e50;">ทำไมต้องเลือกเรา?</h2>
                    <p class="lead" style="color: #34495e;">บริการที่ครบครันและมีคุณภาพระดับโลก</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card glass-card text-center h-100">
                        <div class="card-body p-4">
                            <div class="feature-icon text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px; background: linear-gradient(135deg, #3498db, #2980b9);">
                                <i class="fas fa-user-tie fa-3x"></i>
                            </div>
                            <h4 class="mb-3" style="color: #2c3e50;">ช่างมืออาชีพ</h4>
                            <p style="color: #34495e;">ช่างตัดผมที่มีประสบการณ์และฝีมือเยี่ยม พร้อมให้คำแนะนำทรงผมที่เหมาะกับคุณ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card glass-card text-center h-100">
                        <div class="card-body p-4">
                            <div class="feature-icon text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px; background: linear-gradient(135deg, #27ae60, #229954);">
                                <i class="fas fa-calendar-check fa-3x"></i>
                            </div>
                            <h4 class="mb-3" style="color: #2c3e50;">จองคิวออนไลน์</h4>
                            <p style="color: #34495e;">ระบบจองคิวออนไลน์ที่สะดวก ไม่ต้องรอคิวนาน เลือกเวลาที่เหมาะกับคุณ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card glass-card text-center h-100">
                        <div class="card-body p-4">
                            <div class="feature-icon text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px; background: linear-gradient(135deg, #e67e22, #d35400);">
                                <i class="fas fa-tools fa-3x"></i>
                            </div>
                            <h4 class="mb-3" style="color: #2c3e50;">เครื่องมือทันสมัย</h4>
                            <p style="color: #34495e;">ใช้เครื่องมือและอุปกรณ์ที่ทันสมัย สะอาด และปลอดภัย</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Preview -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-12">
                    <h2 class="display-5 fw-bold">บริการของเรา</h2>
                    <p class="lead text-muted">บริการตัดผมหลากหลายสไตล์</p>
                </div>
            </div>
            <div class="row g-4">
                <?php
                $services = fetchAll("SELECT * FROM services WHERE is_active = 1 LIMIT 6");
                foreach ($services as $service):
                ?>
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-cut fa-3x text-primary mb-3"></i>
                            <h5 class="card-title"><?= sanitize($service['name']) ?></h5>
                            <p class="card-text text-muted"><?= sanitize($service['description']) ?></p>
                            <p class="h5 text-primary"><?= number_format($service['price']) ?> บาท</p>
                            <small class="text-muted">ระยะเวลา <?= $service['duration'] ?> นาที</small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-4">
                <a href="services.php" class="btn btn-primary btn-lg">ดูบริการทั้งหมด</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-cut"></i> Barber Shop</h5>
                    <p>ร้านตัดผมมืออาชีพ ให้บริการด้วยใจ มีคุณภาพ</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                    <p>
                        <i class="fab fa-facebook-f text-primary"></i>
                        <a href="https://www.facebook.com/kittin.singwee.3" target="_blank" class="text-white text-decoration-none">
                            Facebook: Kittin Singwee
                        </a>
                    </p>
                    <p>
                        <i class="fab fa-instagram text-danger"></i>
                        <a href="https://www.instagram.com/kittin__singwee/" target="_blank" class="text-white text-decoration-none">
                            Instagram: @kittin__singwee
                        </a>
                    </p>
                </div>
                <div class="col-md-4">
                    <h5>เวลาทำการ</h5>
                    <p>จันทร์ - เสาร์: 09:00 - 20:00</p>
                    <p>อาทิตย์: 10:00 - 18:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Barber Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>

    <!-- Modern JavaScript Effects -->
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards and feature elements
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.card, .feature-icon').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s ease-out';
                observer.observe(el);
            });
        });

        // Add loading animation to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#')) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<span class="loading-spinner"></span> กำลังโหลด...';
                    this.disabled = true;
                }
            });
        });
    </script>
</body>
</html>
