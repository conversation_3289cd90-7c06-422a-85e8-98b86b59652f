<?php
/**
 * ไฟล์ตรวจสอบการเข้าสู่ระบบและสิทธิ์การเข้าถึง
 * Authentication and Authorization
 */

require_once __DIR__ . '/functions.php';

/**
 * ตรวจสอบว่าผู้ใช้เข้าสู่ระบบหรือไม่
 * หากไม่ได้เข้าสู่ระบบจะ redirect ไปหน้า login
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /barber_booking/public/login.php');
        exit;
    }
}

/**
 * ตรวจสอบสิทธิ์ผู้ดูแลระบบ
 * หากไม่ใช่ admin จะ redirect ไปหน้าหลัก
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: /barber_booking/public/index.php');
        exit;
    }
}

/**
 * ตรวจสอบสิทธิ์ลูกค้า
 * หากไม่ใช่ลูกค้าจะ redirect ไปหน้าหลัก
 */
function requireCustomer() {
    requireLogin();
    if (isAdmin()) {
        header('Location: /barber_booking/admin/dashboard.php');
        exit;
    }
}

/**
 * ป้องกันการเข้าถึงหน้า login/register เมื่อเข้าสู่ระบบแล้ว
 */
function redirectIfLoggedIn() {
    if (isLoggedIn()) {
        if (isAdmin()) {
            header('Location: /barber_booking/admin/dashboard.php');
        } else {
            header('Location: /barber_booking/customer/dashboard.php');
        }
        exit;
    }
}

/**
 * ตรวจสอบสิทธิ์การเข้าถึงข้อมูลการจอง
 * ลูกค้าสามารถดูเฉพาะการจองของตนเอง
 * Admin สามารถดูการจองทั้งหมด
 */
function canAccessBooking($booking_user_id) {
    if (isAdmin()) {
        return true;
    }
    return isset($_SESSION['user_id']) && $_SESSION['user_id'] == $booking_user_id;
}

/**
 * ตรวจสอบสิทธิ์การแก้ไขข้อมูลผู้ใช้
 */
function canEditUser($user_id) {
    if (isAdmin()) {
        return true;
    }
    return isset($_SESSION['user_id']) && $_SESSION['user_id'] == $user_id;
}

/**
 * ล็อกการเข้าสู่ระบบที่ผิดพลาด
 */
function logFailedLogin($username, $ip_address) {
    $log_file = __DIR__ . '/../logs/failed_logins.log';
    $log_entry = date('Y-m-d H:i:s') . " - Failed login attempt for username: $username from IP: $ip_address\n";
    
    // สร้างโฟลเดอร์ logs หากไม่มี
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * ตรวจสอบการพยายามเข้าสู่ระบบที่มากเกินไป
 */
function checkLoginAttempts($ip_address, $max_attempts = 5, $lockout_time = 900) { // 15 นาที
    $log_file = __DIR__ . '/../logs/failed_logins.log';
    
    if (!file_exists($log_file)) {
        return true; // ไม่มีไฟล์ log แสดงว่ายังไม่เคยมีการล็อกอิน fail
    }
    
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    $recent_attempts = 0;
    $cutoff_time = time() - $lockout_time;
    
    foreach ($lines as $line) {
        if (strpos($line, $ip_address) !== false) {
            $timestamp = substr($line, 0, 19);
            $log_time = strtotime($timestamp);
            
            if ($log_time > $cutoff_time) {
                $recent_attempts++;
            }
        }
    }
    
    return $recent_attempts < $max_attempts;
}

/**
 * ล้างข้อมูล session เมื่อมีการเปลี่ยนแปลงสิทธิ์
 */
function refreshUserSession($user_id) {
    $sql = "SELECT * FROM users WHERE id = ?";
    $user = fetchOne($sql, [$user_id]);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        return true;
    }
    
    return false;
}
?>
