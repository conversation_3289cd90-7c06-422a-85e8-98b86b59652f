<?php
/**
 * ไฟล์สำหรับส่งแจ้งเตือนผ่าน LINE Notify
 * LINE Notification System
 */

require_once __DIR__ . '/../includes/functions.php';

// LINE Notify Token (ต้องสมัครและขอ Token จาก LINE Notify)
define('LINE_NOTIFY_TOKEN', 'YOUR_LINE_NOTIFY_TOKEN_HERE');
define('LINE_NOTIFY_API', 'https://notify-api.line.me/api/notify');

/**
 * ส่งข้อความผ่าน LINE Notify
 */
function sendLineNotify($message, $token = null) {
    if (!$token) {
        $token = LINE_NOTIFY_TOKEN;
    }
    
    if (!$token || $token === 'YOUR_LINE_NOTIFY_TOKEN_HERE') {
        error_log('LINE Notify Token not configured');
        return false;
    }
    
    $data = [
        'message' => $message
    ];
    
    $headers = [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/x-www-form-urlencoded'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, LINE_NOTIFY_API);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return true;
    } else {
        error_log("LINE Notify Error: HTTP $http_code - $response");
        return false;
    }
}

/**
 * ส่งแจ้งเตือนการจองใหม่
 */
function sendNewBookingNotification($booking_id) {
    // ดึงข้อมูลการจอง
    $sql = "SELECT b.*, u.full_name as customer_name, u.phone as customer_phone,
                   s.name as service_name, s.price, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";
    
    $booking = fetchOne($sql, [$booking_id]);
    
    if (!$booking) {
        return false;
    }
    
    $booking_date = formatDateThai($booking['booking_date']);
    $booking_time = formatTime12Hour($booking['booking_time']);
    
    $message = "🔔 การจองใหม่!\n\n";
    $message .= "📋 หมายเลขการจอง: #{$booking['id']}\n";
    $message .= "👤 ลูกค้า: {$booking['customer_name']}\n";
    $message .= "📞 โทร: {$booking['customer_phone']}\n";
    $message .= "✂️ บริการ: {$booking['service_name']}\n";
    $message .= "👨‍💼 ช่าง: {$booking['barber_name']}\n";
    $message .= "📅 วันที่: {$booking_date}\n";
    $message .= "⏰ เวลา: {$booking_time}\n";
    $message .= "💰 ราคา: " . number_format($booking['total_price']) . " บาท\n";
    
    if ($booking['notes']) {
        $message .= "📝 หมายเหตุ: {$booking['notes']}\n";
    }
    
    $message .= "\n⚠️ กรุณาตรวจสอบและยืนยันการจอง";
    
    return sendLineNotify($message);
}

/**
 * ส่งแจ้งเตือนการเปลี่ยนสถานะการจอง
 */
function sendBookingStatusNotification($booking_id, $new_status) {
    $sql = "SELECT b.*, u.full_name as customer_name, s.name as service_name, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";
    
    $booking = fetchOne($sql, [$booking_id]);
    
    if (!$booking) {
        return false;
    }
    
    $status_emoji = [
        'confirmed' => '✅',
        'completed' => '🎉',
        'cancelled' => '❌'
    ];
    
    $status_text = [
        'confirmed' => 'ยืนยันแล้ว',
        'completed' => 'เสร็จสิ้น',
        'cancelled' => 'ยกเลิก'
    ];
    
    $emoji = $status_emoji[$new_status] ?? '📝';
    $status = $status_text[$new_status] ?? $new_status;
    
    $booking_date = formatDateThai($booking['booking_date']);
    $booking_time = formatTime12Hour($booking['booking_time']);
    
    $message = "{$emoji} อัพเดทสถานะการจอง\n\n";
    $message .= "📋 หมายเลขการจอง: #{$booking['id']}\n";
    $message .= "👤 ลูกค้า: {$booking['customer_name']}\n";
    $message .= "✂️ บริการ: {$booking['service_name']}\n";
    $message .= "👨‍💼 ช่าง: {$booking['barber_name']}\n";
    $message .= "📅 วันที่: {$booking_date}\n";
    $message .= "⏰ เวลา: {$booking_time}\n";
    $message .= "📊 สถานะใหม่: {$status}";
    
    return sendLineNotify($message);
}

/**
 * ส่งแจ้งเตือนการจองของวันนี้
 */
function sendTodayBookingsReminder() {
    $today_bookings = fetchAll("
        SELECT b.*, u.full_name as customer_name, s.name as service_name, br.name as barber_name
        FROM bookings b
        JOIN users u ON b.user_id = u.id
        JOIN services s ON b.service_id = s.id
        JOIN barbers br ON b.barber_id = br.id
        WHERE b.booking_date = CURDATE() AND b.status IN ('pending', 'confirmed')
        ORDER BY b.booking_time ASC
    ");
    
    if (empty($today_bookings)) {
        $message = "📅 วันนี้ (" . formatDateThai(date('Y-m-d')) . ")\n";
        $message .= "ไม่มีการจองคิว 😊";
        return sendLineNotify($message);
    }
    
    $message = "📅 การจองวันนี้ (" . formatDateThai(date('Y-m-d')) . ")\n";
    $message .= "รวม " . count($today_bookings) . " การจอง\n\n";
    
    foreach ($today_bookings as $booking) {
        $time = formatTime12Hour($booking['booking_time']);
        $status_emoji = $booking['status'] === 'confirmed' ? '✅' : '⏳';
        
        $message .= "{$status_emoji} {$time} - {$booking['customer_name']}\n";
        $message .= "   ✂️ {$booking['service_name']} | 👨‍💼 {$booking['barber_name']}\n\n";
    }
    
    $message .= "💪 ขอให้ทำงานสนุกนะครับ!";
    
    return sendLineNotify($message);
}

/**
 * ส่งแจ้งเตือนสรุปรายวัน
 */
function sendDailySummary() {
    $today = date('Y-m-d');
    
    // นับการจองวันนี้
    $total_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE booking_date = ?", [$today])['count'];
    $completed_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE booking_date = ? AND status = 'completed'", [$today])['count'];
    $cancelled_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE booking_date = ? AND status = 'cancelled'", [$today])['count'];
    
    // คำนวณรายได้
    $revenue = fetchOne("SELECT COALESCE(SUM(total_price), 0) as revenue FROM bookings WHERE booking_date = ? AND status = 'completed'", [$today])['revenue'];
    
    $message = "📊 สรุปประจำวัน (" . formatDateThai($today) . ")\n\n";
    $message .= "📋 การจองทั้งหมด: {$total_bookings} ครั้ง\n";
    $message .= "✅ เสร็จสิ้น: {$completed_bookings} ครั้ง\n";
    $message .= "❌ ยกเลิก: {$cancelled_bookings} ครั้ง\n";
    $message .= "💰 รายได้: " . number_format($revenue) . " บาท\n\n";
    
    if ($completed_bookings > 0) {
        $avg_revenue = $revenue / $completed_bookings;
        $message .= "📈 รายได้เฉลี่ยต่อครั้ง: " . number_format($avg_revenue) . " บาท\n";
    }
    
    $message .= "\n🎉 ขอบคุณสำหรับการทำงานหนักวันนี้!";
    
    return sendLineNotify($message);
}

/**
 * ส่งแจ้งเตือนลูกค้าใหม่
 */
function sendNewCustomerNotification($user_id) {
    $user = fetchOne("SELECT * FROM users WHERE id = ? AND role = 'customer'", [$user_id]);
    
    if (!$user) {
        return false;
    }
    
    $message = "🎉 ลูกค้าใหม่สมัครสมาชิก!\n\n";
    $message .= "👤 ชื่อ: {$user['full_name']}\n";
    $message .= "📧 อีเมล: {$user['email']}\n";
    $message .= "📞 โทร: {$user['phone']}\n";
    $message .= "📅 สมัครเมื่อ: " . date('d/m/Y H:i', strtotime($user['created_at'])) . "\n\n";
    $message .= "🎯 อย่าลืมติดตามและให้บริการที่ดีนะครับ!";
    
    return sendLineNotify($message);
}

/**
 * ส่งแจ้งเตือนเมื่อมีข้อความติดต่อใหม่
 */
function sendContactFormNotification($name, $email, $subject, $message_text) {
    $message = "📧 ข้อความติดต่อใหม่!\n\n";
    $message .= "👤 ชื่อ: {$name}\n";
    $message .= "📧 อีเมล: {$email}\n";
    $message .= "📋 หัวข้อ: {$subject}\n";
    $message .= "💬 ข้อความ: {$message_text}\n\n";
    $message .= "⚡ กรุณาตอบกลับโดยเร็วที่สุด";
    
    return sendLineNotify($message);
}

/**
 * ทดสอบการส่ง LINE Notify
 */
function testLineNotify() {
    $message = "🧪 ทดสอบระบบแจ้งเตือน LINE Notify\n";
    $message .= "📅 เวลา: " . date('d/m/Y H:i:s') . "\n";
    $message .= "✅ ระบบทำงานปกติ";
    
    return sendLineNotify($message);
}

// ตัวอย่างการใช้งาน (สำหรับทดสอบ)
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    // ถ้าเรียกไฟล์นี้โดยตรง ให้ทดสอบการส่ง
    if (isset($_GET['test'])) {
        if (testLineNotify()) {
            echo "ส่งข้อความทดสอบสำเร็จ";
        } else {
            echo "ส่งข้อความทดสอบไม่สำเร็จ";
        }
    } else {
        echo "เพิ่ม ?test=1 เพื่อทดสอบการส่ง LINE Notify";
    }
}
?>
