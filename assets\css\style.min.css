@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
:root{--primary-color:#1a1a2e;--secondary-color:#16213e;--accent-color:#e94560;--gold-color:#ffd700;--dark-color:#0f0f23;--light-color:#f8f9fa;--text-light:#ffffff;--text-dark:#2c3e50;--gradient-primary:linear-gradient(135deg,#667eea 0%,#764ba2 100%);--gradient-secondary:linear-gradient(135deg,#f093fb 0%,#f5576c 100%);--gradient-gold:linear-gradient(135deg,#ffd700 0%,#ffed4e 100%);--gradient-dark:linear-gradient(135deg,#1a1a2e 0%,#16213e 100%);--shadow-soft:0 10px 30px rgba(0,0,0,0.1);--shadow-medium:0 15px 35px rgba(0,0,0,0.15);--shadow-strong:0 20px 40px rgba(0,0,0,0.2);--border-radius:15px;--transition:all 0.3s cubic-bezier(0.4,0,0.2,1)}
*{font-family:'Poppins',sans-serif;box-sizing:border-box}
h1,h2,h3,h4,h5,h6{color:#2c3e50;text-shadow:0 1px 3px rgba(0,0,0,0.1);font-weight:600}
p,span,div{color:#34495e;text-shadow:0 1px 2px rgba(0,0,0,0.05)}
.hero-content h1{color:#ffffff!important;text-shadow:0 2px 10px rgba(0,0,0,0.5)}
.hero-content p{color:#ecf0f1!important;text-shadow:0 1px 5px rgba(0,0,0,0.3)}
html{scroll-behavior:smooth}
body{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 50%,#dee2e6 100%);background-attachment:fixed;color:#2c3e50;line-height:1.7;overflow-x:hidden;font-weight:400}
.container{position:relative;z-index:1}
.glass-card{background:rgba(255,255,255,0.9);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.8);border-radius:var(--border-radius);box-shadow:0 8px 32px rgba(0,0,0,0.1);color:#2c3e50}
.card{border:none;border-radius:var(--border-radius);box-shadow:0 4px 20px rgba(0,0,0,0.08);transition:var(--transition);overflow:hidden;background:rgba(255,255,255,0.98);backdrop-filter:blur(10px);color:#2c3e50}
.card:hover{transform:translateY(-5px);box-shadow:var(--shadow-strong)}
.card-header{background:var(--gradient-primary);color:var(--text-light);border:none;padding:1.5rem;font-weight:600}
.btn{border-radius:50px;padding:12px 30px;font-weight:500;text-transform:uppercase;letter-spacing:1px;transition:var(--transition);border:none;position:relative;overflow:hidden}
.btn::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:var(--transition)}
.btn:hover::before{left:100%}
.btn-primary{background:linear-gradient(135deg,#3498db,#2980b9);color:#ffffff;box-shadow:0 4px 15px rgba(52,152,219,0.3);border:none}
.btn-primary:hover{background:linear-gradient(135deg,#2980b9,#1f5f8b);transform:translateY(-2px);box-shadow:0 6px 20px rgba(52,152,219,0.4);color:#ffffff}
.btn-success{background:linear-gradient(135deg,#27ae60,#229954);color:#ffffff;box-shadow:0 4px 15px rgba(39,174,96,0.3);border:none}
.btn-success:hover{background:linear-gradient(135deg,#229954,#1e7e34);transform:translateY(-2px);box-shadow:0 6px 20px rgba(39,174,96,0.4);color:#ffffff}
.btn-danger{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#ffffff;box-shadow:0 4px 15px rgba(231,76,60,0.3);border:none}
.btn-danger:hover{background:linear-gradient(135deg,#c0392b,#a93226);transform:translateY(-2px);box-shadow:0 6px 20px rgba(231,76,60,0.4);color:#ffffff}
.navbar{background:rgba(255,255,255,0.95)!important;backdrop-filter:blur(10px);border-bottom:1px solid rgba(0,0,0,0.1);padding:1rem 0;transition:var(--transition);box-shadow:0 2px 10px rgba(0,0,0,0.1)}
.navbar.scrolled{background:rgba(255,255,255,0.98)!important;box-shadow:0 4px 20px rgba(0,0,0,0.15)}
.navbar-brand{font-weight:700;font-size:1.5rem;color:#2c3e50!important;text-decoration:none;text-shadow:0 1px 3px rgba(0,0,0,0.3)}
.navbar-nav .nav-link{color:#2c3e50!important;font-weight:500;margin:0 10px;padding:10px 20px!important;border-radius:25px;transition:var(--transition);position:relative}
.navbar-nav .nav-link:hover{background:rgba(52,152,219,0.1);color:#3498db!important;transform:translateY(-2px)}
.navbar-nav .nav-link.active{background:linear-gradient(135deg,#3498db,#2980b9);color:#ffffff!important;box-shadow:0 4px 15px rgba(52,152,219,0.3)}
.hero-section{min-height:100vh;display:flex;align-items:center;position:relative;overflow:hidden;background:linear-gradient(135deg,#2c3e50 0%,#34495e 50%,#3498db 100%);color:#ffffff}
.hero-section::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');opacity:0.3}
.hero-content{position:relative;z-index:2;text-align:center;color:var(--text-light)}
.hero-title{font-size:4rem;font-weight:800;margin-bottom:1rem;background:var(--gradient-gold);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;animation:fadeInUp 1s ease-out}
.hero-subtitle{font-size:1.5rem;font-weight:300;margin-bottom:2rem;opacity:0.9;animation:fadeInUp 1s ease-out 0.2s both}
.hero-buttons{animation:fadeInUp 1s ease-out 0.4s both}
@keyframes fadeInUp{from{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}
@keyframes pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.05)}}
.pulse-animation{animation:pulse 2s infinite}
@keyframes bounce{0%,20%,50%,80%,100%{transform:translateY(0)}40%{transform:translateY(-10px)}60%{transform:translateY(-5px)}}
.form-control{border:2px solid rgba(255,255,255,0.1);border-radius:var(--border-radius);background:rgba(255,255,255,0.9);backdrop-filter:blur(10px);padding:15px 20px;font-size:1rem;transition:var(--transition)}
.form-control:focus{border-color:var(--accent-color);box-shadow:0 0 0 0.2rem rgba(233,69,96,0.25);background:rgba(255,255,255,1);transform:translateY(-2px)}
.form-select{border:2px solid rgba(255,255,255,0.1);border-radius:var(--border-radius);background:rgba(255,255,255,0.9);backdrop-filter:blur(10px);padding:15px 20px;transition:var(--transition)}
.form-select:focus{border-color:var(--accent-color);box-shadow:0 0 0 0.2rem rgba(233,69,96,0.25)}
.gallery-thumb{position:relative;overflow:hidden;border-radius:var(--border-radius);transition:var(--transition);box-shadow:var(--shadow-soft)}
.gallery-thumb:hover{transform:scale(1.05) rotate(1deg);box-shadow:var(--shadow-strong)}
.gallery-thumb img{transition:var(--transition)}
.gallery-thumb:hover img{transform:scale(1.1)}
.loading-spinner{display:inline-block;width:40px;height:40px;border:4px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:var(--accent-color);animation:spin 1s ease-in-out infinite}
@keyframes spin{to{transform:rotate(360deg)}}
.alert{border:none;border-radius:var(--border-radius);padding:1.5rem;margin-bottom:1.5rem;backdrop-filter:blur(10px);border-left:4px solid;box-shadow:0 4px 15px rgba(0,0,0,0.05)}
.alert-success{background:rgba(39,174,96,0.1);border-left-color:#27ae60;color:#1e7e34;text-shadow:0 1px 2px rgba(0,0,0,0.1)}
.alert-danger{background:rgba(231,76,60,0.1);border-left-color:#e74c3c;color:#a93226;text-shadow:0 1px 2px rgba(0,0,0,0.1)}
.alert-warning{background:rgba(243,156,18,0.1);border-left-color:#f39c12;color:#b7950b;text-shadow:0 1px 2px rgba(0,0,0,0.1)}
.alert-info{background:rgba(52,152,219,0.1);border-left-color:#3498db;color:#1f5f8b;text-shadow:0 1px 2px rgba(0,0,0,0.1)}
.table{background:rgba(255,255,255,0.98);backdrop-filter:blur(10px);border-radius:var(--border-radius);overflow:hidden;box-shadow:0 4px 20px rgba(0,0,0,0.08);color:#2c3e50}
.table thead th{background:linear-gradient(135deg,#3498db,#2980b9);color:#ffffff;border:none;padding:1rem;font-weight:600;text-shadow:0 1px 3px rgba(0,0,0,0.3)}
.table tbody tr{transition:var(--transition);color:#2c3e50}
.table tbody tr:hover{background:rgba(52,152,219,0.1);transform:scale(1.01)}
.table tbody td{color:#34495e;text-shadow:0 1px 2px rgba(0,0,0,0.05)}
.badge{padding:8px 15px;border-radius:20px;font-weight:500;text-transform:uppercase;letter-spacing:1px}
.floating-element{animation:float 3s ease-in-out infinite}
@keyframes float{0%,100%{transform:translateY(0px)}50%{transform:translateY(-10px)}}
::-webkit-scrollbar{width:8px}
::-webkit-scrollbar-track{background:rgba(255,255,255,0.1)}
::-webkit-scrollbar-thumb{background:var(--gradient-primary);border-radius:4px}
::-webkit-scrollbar-thumb:hover{background:var(--gradient-secondary)}
.footer{background:linear-gradient(135deg,#2c3e50 0%,#34495e 100%);color:#ffffff;padding:3rem 0 1rem;margin-top:5rem;position:relative}
.footer::before{content:'';position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(135deg,#3498db,#27ae60)}
.stats-card{background:var(--gradient-primary);color:var(--text-light);border-radius:var(--border-radius);padding:2rem;text-align:center;transition:var(--transition);position:relative;overflow:hidden}
.stats-card::before{content:'';position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,0.1) 0%,transparent 70%);transform:scale(0);transition:var(--transition)}
.stats-card:hover::before{transform:scale(1)}
.stats-card:hover{transform:translateY(-10px) scale(1.05);box-shadow:var(--shadow-strong)}
.stats-number{font-size:3rem;font-weight:800;margin-bottom:0.5rem;background:var(--gradient-gold);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}
@media print{.no-print{display:none!important}.page-break{page-break-before:always}}
