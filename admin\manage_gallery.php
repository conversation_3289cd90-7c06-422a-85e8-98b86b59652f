<?php
/**
 * หน้าจัดการแกลเลอรี่สำหรับแอดมิน
 */

require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ตรวจสอบการส่งฟอร์มเพิ่มรูปภาพ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];


    
    if ($action === 'add_image') {
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $category = trim($_POST['category'] ?? 'general');
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        // ตรวจสอบ CSRF Token
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
        }
        // ตรวจสอบข้อมูลที่จำเป็น
        elseif (empty($title)) {
            $error_message = 'กรุณากรอกชื่อรูปภาพ';
        }
        // ตรวจสอบการอัพโหลดไฟล์
        elseif (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'ไฟล์ใหญ่เกิน upload_max_filesize',
                UPLOAD_ERR_FORM_SIZE => 'ไฟล์ใหญ่เกิน MAX_FILE_SIZE',
                UPLOAD_ERR_PARTIAL => 'อัปโหลดไฟล์ไม่สมบูรณ์',
                UPLOAD_ERR_NO_FILE => 'ไม่มีไฟล์ที่อัปโหลด',
                UPLOAD_ERR_NO_TMP_DIR => 'ไม่มีโฟลเดอร์ temp',
                UPLOAD_ERR_CANT_WRITE => 'ไม่สามารถเขียนไฟล์ได้',
                UPLOAD_ERR_EXTENSION => 'Extension หยุดการอัปโหลด'
            ];
            $error_code = $_FILES['image']['error'] ?? UPLOAD_ERR_NO_FILE;
            $error_message = 'เกิดข้อผิดพลาดในการอัปโหลด: ' . ($upload_errors[$error_code] ?? 'ไม่ทราบสาเหตุ');


        }
        else {
            $file = $_FILES['image'];
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            // ตรวจสอบประเภทไฟล์
            if (!in_array($file['type'], $allowed_types)) {
                $error_message = 'รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น';
            }
            // ตรวจสอบขนาดไฟล์
            elseif ($file['size'] > $max_size) {
                $error_message = 'ขนาดไฟล์ต้องไม่เกิน 5MB';
            }
            else {
                // สร้างชื่อไฟล์ใหม่
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $filename = 'gallery_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
                $upload_path = __DIR__ . '/../assets/img/gallery/' . $filename;
                
                // สร้างโฟลเดอร์ถ้ายังไม่มี
                $gallery_dir = __DIR__ . '/../assets/img/gallery/';
                if (!is_dir($gallery_dir)) {
                    if (!mkdir($gallery_dir, 0755, true)) {
                        $error_message = 'ไม่สามารถสร้างโฟลเดอร์ gallery ได้';
                    }
                }

                // ตรวจสอบสิทธิ์การเขียน
                if (!$error_message && !is_writable($gallery_dir)) {
                    $error_message = 'ไม่สามารถเขียนไฟล์ในโฟลเดอร์ gallery ได้';
                }
                
                // อัพโหลดไฟล์
                if (!$error_message) {
                    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                        // บันทึกข้อมูลลงฐานข้อมูล
                        $sql = "INSERT INTO gallery (title, description, image, category, is_active, created_at)
                                VALUES (?, ?, ?, ?, 1, NOW())";
                        $result = executeQuery($sql, [$title, $description, $filename, $category]);

                        if ($result) {
                            $success_message = 'เพิ่มรูปภาพเรียบร้อยแล้ว';
                        } else {
                            $error_message = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล';
                            // ลบไฟล์ที่อัพโหลดแล้ว
                            if (file_exists($upload_path)) {
                                unlink($upload_path);
                            }
                        }
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์ ตรวจสอบสิทธิ์โฟลเดอร์';
                    }
                }
            }
        }
    }
    elseif ($action === 'delete_image') {
        $image_id = intval($_POST['image_id'] ?? 0);
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง';
        } elseif ($image_id <= 0) {
            $error_message = 'ไม่พบรูปภาพที่ต้องการลบ';
        } else {
            // ดึงข้อมูลรูปภาพ
            $image = fetchOne("SELECT * FROM gallery WHERE id = ?", [$image_id]);
            
            if ($image) {
                // ลบไฟล์รูปภาพ
                $image_path = __DIR__ . '/../assets/img/gallery/' . $image['image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
                
                // ลบข้อมูลจากฐานข้อมูล
                $result = executeQuery("DELETE FROM gallery WHERE id = ?", [$image_id]);
                
                if ($result) {
                    $success_message = 'ลบรูปภาพเรียบร้อยแล้ว';
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการลบข้อมูล';
                }
            } else {
                $error_message = 'ไม่พบรูปภาพที่ต้องการลบ';
            }
        }
    }
    elseif ($action === 'toggle_status') {
        $image_id = intval($_POST['image_id'] ?? 0);
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง';
        } elseif ($image_id <= 0) {
            $error_message = 'ไม่พบรูปภาพ';
        } else {
            $result = executeQuery("UPDATE gallery SET is_active = NOT is_active WHERE id = ?", [$image_id]);
            
            if ($result) {
                $success_message = 'อัพเดทสถานะเรียบร้อยแล้ว';
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการอัพเดทสถานะ';
            }
        }
    }
}

// ดึงรูปภาพทั้งหมด
$images = fetchAll("SELECT * FROM gallery ORDER BY created_at DESC");

// นับจำนวนรูปภาพ
$total_images = count($images);
$active_images = count(array_filter($images, function($img) { return $img['is_active']; }));
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแกลเลอรี่ - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        /* Performance optimizations */
        * {
            box-sizing: border-box;
        }

        .gallery-grid {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            will-change: transform;
        }

        .gallery-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        
        .gallery-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.2s ease;
            will-change: transform;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
            pointer-events: auto;
        }
        
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        


        /* Mobile optimizations */
        @media (max-width: 768px) {
            .gallery-item:hover {
                transform: none;
            }

            .gallery-item:hover img {
                transform: none;
            }

            .gallery-overlay {
                opacity: 1;
                background: rgba(0,0,0,0.5);
            }


        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
            .gallery-item,
            .gallery-item img,
            .gallery-overlay {
                transition: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_bookings.php">จัดการการจอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php">จัดการผู้ใช้</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_gallery.php">จัดการแกลเลอรี่</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-images text-primary"></i> จัดการแกลเลอรี่
                </h2>
                <p class="text-muted">เพิ่ม แก้ไข และจัดการรูปภาพผลงาน</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addImageModal">
                    <i class="fas fa-plus"></i> เพิ่มรูปภาพ
                </button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>รูปภาพทั้งหมด</h5>
                                <h2><?= $total_images ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-images fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>รูปภาพที่แสดง</h5>
                                <h2><?= $active_images ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-eye fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>รูปภาพที่ซ่อน</h5>
                                <h2><?= $total_images - $active_images ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-eye-slash fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>ดูแกลเลอรี่</h5>
                                <p class="mb-0">หน้าเว็บไซต์</p>
                            </div>
                            <div class="align-self-center">
                                <a href="../public/gallery.php" class="text-white">
                                    <i class="fas fa-external-link-alt fa-2x"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Gallery Grid -->
        <div class="row">
            <?php if (empty($images)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">ยังไม่มีรูปภาพในแกลเลอรี่</h4>
                        <p class="text-muted">คลิกปุ่ม "เพิ่มรูปภาพ" เพื่อเริ่มต้น</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addImageModal">
                            <i class="fas fa-plus"></i> เพิ่มรูปภาพแรก
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($images as $image): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="gallery-item">
                            <span class="status-badge">
                                <?php if ($image['is_active']): ?>
                                    <span class="badge bg-success">แสดง</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">ซ่อน</span>
                                <?php endif; ?>
                            </span>
                            
                            <img src="../assets/img/gallery/<?= sanitize($image['image']) ?>"
                                 alt="<?= sanitize($image['title']) ?>"
                                 onerror="this.src='../assets/img/placeholder.jpg'">
                            
                            <div class="gallery-overlay">
                                <h6 class="text-center mb-2"><?= sanitize($image['title']) ?></h6>
                                <?php if ($image['description']): ?>
                                    <p class="text-center small mb-3"><?= sanitize($image['description']) ?></p>
                                <?php endif; ?>
                                
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-light" 
                                            onclick="toggleImageStatus(<?= $image['id'] ?>)">
                                        <i class="fas fa-<?= $image['is_active'] ? 'eye-slash' : 'eye' ?>"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteImage(<?= $image['id'] ?>, '<?= sanitize($image['title']) ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Image Modal -->
    <div class="modal fade" id="addImageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus"></i> เพิ่มรูปภาพใหม่
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_image">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพ *</label>
                            <input type="file" class="form-control" id="image" name="image"
                                   accept="image/*" required>
                            <div class="form-text">รองรับไฟล์ JPG, PNG, GIF ขนาดไม่เกิน 5MB</div>
                            <div class="invalid-feedback">กรุณาเลือกไฟล์รูปภาพ</div>

                            <!-- Preview -->
                            <div id="imagePreview" class="mt-3" style="display: none;">
                                <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อรูปภาพ *</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                    <div class="invalid-feedback">กรุณากรอกชื่อรูปภาพ</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category" class="form-label">หมวดหมู่</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="general">ทั่วไป</option>
                                        <option value="haircut">ตัดผม</option>
                                        <option value="styling">จัดแต่งทรงผม</option>
                                        <option value="beard">โกนหนวด</option>
                                        <option value="treatment">ดูแลผม</option>
                                        <option value="before_after">ก่อน-หลัง</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบาย</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="คำอธิบายเกี่ยวกับรูปภาพ (ไม่บังคับ)"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึก
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden Forms for Actions -->
    <form id="toggleStatusForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        <input type="hidden" name="image_id" id="toggleImageId">
    </form>

    <form id="deleteImageForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_image">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        <input type="hidden" name="image_id" id="deleteImageId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // File upload handling
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('image');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');

            // File input change
            fileInput.addEventListener('change', function(e) {
                handleFileSelect(e.target.files[0]);
            });

            function handleFileSelect(file) {
                if (file) {
                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น');
                        fileInput.value = '';
                        return;
                    }

                    // Validate file size (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('ขนาดไฟล์ต้องไม่เกิน 5MB');
                        fileInput.value = '';
                        return;
                    }

                    // Show preview
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        imagePreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Auto-fill title if empty
                    const titleInput = document.getElementById('title');
                    if (!titleInput.value) {
                        const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
                        titleInput.value = fileName;
                    }
                }
            }
        });

        // Toggle image status
        function toggleImageStatus(imageId) {
            document.getElementById('toggleImageId').value = imageId;
            document.getElementById('toggleStatusForm').submit();
        }

        // Delete image
        function deleteImage(imageId, imageName) {
            if (confirm(`คุณต้องการลบรูปภาพ "${imageName}" หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้`)) {
                document.getElementById('deleteImageId').value = imageId;
                document.getElementById('deleteImageForm').submit();
            }
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
