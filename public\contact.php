<?php
require_once __DIR__ . '/../includes/functions.php';

$success_message = '';
$error_message = '';

// ตรวจสอบการส่งฟอร์มติดต่อ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
    }
    // ตรวจสอบรูปแบบอีเมล
    elseif (!isValidEmail($email)) {
        $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
    }
    else {
        // ส่งอีเมลติดต่อ (ในที่นี้จะจำลองการส่ง)
        $to = '<EMAIL>';
        $email_subject = "ติดต่อจากเว็บไซต์: " . $subject;
        $email_body = "
        ชื่อ: $name
        อีเมล: $email
        โทรศัพท์: $phone
        หัวข้อ: $subject
        
        ข้อความ:
        $message
        
        ส่งจาก: เว็บไซต์ Barber Shop
        เวลา: " . date('Y-m-d H:i:s');
        
        $headers = "From: $email\r\n";
        $headers .= "Reply-To: $email\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        
        // ในการใช้งานจริงควรใช้ PHPMailer หรือบริการส่งอีเมลอื่น
        if (mail($to, $email_subject, $email_body, $headers)) {
            $success_message = 'ส่งข้อความเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด';
        } else {
            $error_message = 'เกิดข้อผิดพลาดในการส่งข้อความ กรุณาลองใหม่อีกครั้ง';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ติดต่อเรา - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="promotion.php">โปรโมชั่น</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">แกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">เกี่ยวกับเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว</a></li>
                                    <li><a class="dropdown-item" href="../customer/my_bookings.php">การจองของฉัน</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 bg-info text-white" style="margin-top: 76px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-phone"></i> ติดต่อเรา
                    </h1>
                    <p class="lead">เราพร้อมให้บริการและตอบคำถามของคุณ</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <h3 class="mb-4">
                                <i class="fas fa-info-circle text-primary"></i> ข้อมูลติดต่อ
                            </h3>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5>ที่อยู่</h5>
                                        <p class="text-muted">
                                            123 ถนนสุขุมวิท แขวงคลองเตย<br>
                                            เขตคลองเตย กรุงเทพมหานคร 10110
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-phone fa-2x text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5>โทรศัพท์</h5>
                                        <p class="text-muted">
                                            <a href="tel:0812345678" class="text-decoration-none">************</a><br>
                                            <a href="tel:0212345678" class="text-decoration-none">02-123-4567</a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-envelope fa-2x text-warning"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5>อีเมล</h5>
                                        <p class="text-muted">
                                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a><br>
                                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-clock fa-2x text-info"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5>เวลาทำการ</h5>
                                        <p class="text-muted">
                                            <strong>จันทร์ - เสาร์:</strong> 09:00 - 20:00<br>
                                            <strong>อาทิตย์:</strong> 10:00 - 18:00<br>
                                            <strong>วันหยุดนักขัตฤกษ์:</strong> ปิด
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Social Media -->
                            <div class="contact-item">
                                <h5 class="mb-3">ติดตามเรา</h5>
                                <div class="d-flex gap-3 flex-wrap">
                                    <a href="https://www.facebook.com/kittin.singwee.3" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook-f"></i> Facebook
                                    </a>
                                    <a href="https://www.instagram.com/kittin__singwee/" target="_blank" class="btn btn-outline-danger btn-sm">
                                        <i class="fab fa-instagram"></i> Instagram
                                    </a>
                                    <a href="#" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-line"></i> LINE
                                    </a>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h3 class="mb-4">
                                <i class="fas fa-paper-plane text-primary"></i> ส่งข้อความถึงเรา
                            </h3>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">
                                                <i class="fas fa-user"></i> ชื่อ-นามสกุล *
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="name" 
                                                   name="name" 
                                                   value="<?= sanitize($_POST['name'] ?? '') ?>"
                                                   required>
                                            <div class="invalid-feedback">กรุณากรอกชื่อ-นามสกุล</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> อีเมล *
                                            </label>
                                            <input type="email" 
                                                   class="form-control" 
                                                   id="email" 
                                                   name="email" 
                                                   value="<?= sanitize($_POST['email'] ?? '') ?>"
                                                   required>
                                            <div class="invalid-feedback">กรุณากรอกอีเมลที่ถูกต้อง</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone"></i> เบอร์โทรศัพท์
                                            </label>
                                            <input type="tel" 
                                                   class="form-control" 
                                                   id="phone" 
                                                   name="phone" 
                                                   value="<?= sanitize($_POST['phone'] ?? '') ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="subject" class="form-label">
                                                <i class="fas fa-tag"></i> หัวข้อ *
                                            </label>
                                            <select class="form-select" id="subject" name="subject" required>
                                                <option value="">-- เลือกหัวข้อ --</option>
                                                <option value="จองคิว" <?= ($_POST['subject'] ?? '') === 'จองคิว' ? 'selected' : '' ?>>จองคิว</option>
                                                <option value="สอบถามบริการ" <?= ($_POST['subject'] ?? '') === 'สอบถามบริการ' ? 'selected' : '' ?>>สอบถามบริการ</option>
                                                <option value="ร้องเรียน" <?= ($_POST['subject'] ?? '') === 'ร้องเรียน' ? 'selected' : '' ?>>ร้องเรียน</option>
                                                <option value="ข้อเสนอแนะ" <?= ($_POST['subject'] ?? '') === 'ข้อเสนอแนะ' ? 'selected' : '' ?>>ข้อเสนอแนะ</option>
                                                <option value="อื่นๆ" <?= ($_POST['subject'] ?? '') === 'อื่นๆ' ? 'selected' : '' ?>>อื่นๆ</option>
                                            </select>
                                            <div class="invalid-feedback">กรุณาเลือกหัวข้อ</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">
                                        <i class="fas fa-comment"></i> ข้อความ *
                                    </label>
                                    <textarea class="form-control" 
                                              id="message" 
                                              name="message" 
                                              rows="5" 
                                              placeholder="กรุณาระบุรายละเอียดที่ต้องการติดต่อ..."
                                              required><?= sanitize($_POST['message'] ?? '') ?></textarea>
                                    <div class="invalid-feedback">กรุณากรอกข้อความ</div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane"></i> ส่งข้อความ
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-map-marked-alt text-primary"></i> แผนที่ร้าน
                    </h3>
                    
                    <!-- Google Maps Embed -->
                    <div class="ratio ratio-21x9">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3875.5397!2d100.5417!3d13.7563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTPCsDQ1JzIyLjciTiAxMDDCsDMyJzMwLjEiRQ!5e0!3m2!1sth!2sth!4v1234567890"
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy" 
                                referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="https://goo.gl/maps/example" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> เปิดใน Google Maps
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-question-circle text-primary"></i> คำถามที่พบบ่อย
                    </h3>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    จองคิวล่วงหน้าได้กี่วัน?
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    สามารถจองคิวล่วงหน้าได้สูงสุด 30 วัน และต้องจองอย่างน้อย 1 วันล่วงหน้า
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    สามารถยกเลิกการจองได้หรือไม่?
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    สามารถยกเลิกการจองได้ แต่ต้องยกเลิกก่อนเวลานัดหมายอย่างน้อย 2 ชั่วโมง
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    มีที่จอดรถหรือไม่?
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    มีที่จอดรถให้บริการฟรี สามารถจอดได้ประมาณ 10 คัน หรือสามารถจอดตามถนนได้
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                    รับชำระเงินแบบไหนบ้าง?
                                </button>
                            </h2>
                            <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    รับชำระเงินสด, โอนเงิน, บัตรเครดิต และ QR Code PromptPay
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-cut"></i> Barber Shop</h5>
                    <p>ร้านตัดผมมืออาชีพ ให้บริการด้วยใจ มีคุณภาพ</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                    <p>
                        <i class="fab fa-facebook-f text-primary"></i>
                        <a href="https://www.facebook.com/kittin.singwee.3" target="_blank" class="text-white text-decoration-none">
                            Facebook: Kittin Singwee
                        </a>
                    </p>
                    <p>
                        <i class="fab fa-instagram text-danger"></i>
                        <a href="https://www.instagram.com/kittin__singwee/" target="_blank" class="text-white text-decoration-none">
                            Instagram: @kittin__singwee
                        </a>
                    </p>
                </div>
                <div class="col-md-4">
                    <h5>เวลาทำการ</h5>
                    <p>จันทร์ - เสาร์: 09:00 - 20:00</p>
                    <p>อาทิตย์: 10:00 - 18:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Barber Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
