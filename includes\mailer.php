<?php
/**
 * ไฟล์สำหรับส่งอีเมลแจ้งเตือน
 * Email Notification System
 */

// ใช้ PHPMailer (ต้องติดตั้งผ่าน Composer หรือดาวน์โหลดแยก)
// require_once __DIR__ . '/../vendor/autoload.php';
// use PHPMailer\PHPMailer\PHPMailer;
// use PHPMailer\PHPMailer\SMTP;
// use PHPMailer\PHPMailer\Exception;

/**
 * การตั้งค่าอีเมล
 */
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'ร้านตัดผม Barber Shop');

/**
 * ส่งอีเมลแจ้งเตือนการจองคิว
 */
function sendBookingConfirmation($booking_id) {
    // ดึงข้อมูลการจอง
    $sql = "SELECT b.*, u.email, u.full_name, s.name as service_name, s.price, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";
    
    $booking = fetchOne($sql, [$booking_id]);
    
    if (!$booking) {
        return false;
    }
    
    $subject = "ยืนยันการจองคิว - ร้านตัดผม Barber Shop";
    $message = createBookingEmailTemplate($booking);
    
    return sendEmail($booking['email'], $booking['full_name'], $subject, $message);
}

/**
 * ส่งอีเมลแจ้งเตือนการเปลี่ยนแปลงสถานะการจอง
 */
function sendBookingStatusUpdate($booking_id, $new_status) {
    $sql = "SELECT b.*, u.email, u.full_name, s.name as service_name, br.name as barber_name
            FROM bookings b
            JOIN users u ON b.user_id = u.id
            JOIN services s ON b.service_id = s.id
            JOIN barbers br ON b.barber_id = br.id
            WHERE b.id = ?";
    
    $booking = fetchOne($sql, [$booking_id]);
    
    if (!$booking) {
        return false;
    }
    
    $status_text = [
        'confirmed' => 'ยืนยันแล้ว',
        'completed' => 'เสร็จสิ้น',
        'cancelled' => 'ยกเลิก'
    ];
    
    $subject = "แจ้งเตือนสถานะการจอง - " . $status_text[$new_status];
    $message = createStatusUpdateEmailTemplate($booking, $new_status, $status_text[$new_status]);
    
    return sendEmail($booking['email'], $booking['full_name'], $subject, $message);
}

/**
 * ฟังก์ชันส่งอีเมลหลัก (ใช้ mail() function ของ PHP)
 */
function sendEmail($to_email, $to_name, $subject, $message) {
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . FROM_NAME . " <" . FROM_EMAIL . ">" . "\r\n";
    $headers .= "Reply-To: " . FROM_EMAIL . "\r\n";
    
    return mail($to_email, $subject, $message, $headers);
}

/**
 * สร้างเทมเพลตอีเมลสำหรับการจองคิว
 */
function createBookingEmailTemplate($booking) {
    $booking_date = formatDateThai($booking['booking_date']);
    $booking_time = formatTime12Hour($booking['booking_time']);
    
    $html = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>ยืนยันการจองคิว</h1>
                <p>ร้านตัดผม Barber Shop</p>
            </div>
            
            <div class='content'>
                <p>เรียน คุณ{$booking['full_name']}</p>
                <p>ขอบคุณที่ใช้บริการของเรา การจองคิวของท่านได้รับการยืนยันแล้ว</p>
                
                <div class='booking-details'>
                    <h3>รายละเอียดการจอง</h3>
                    <p><strong>หมายเลขการจอง:</strong> #{$booking['id']}</p>
                    <p><strong>บริการ:</strong> {$booking['service_name']}</p>
                    <p><strong>ช่างตัดผม:</strong> {$booking['barber_name']}</p>
                    <p><strong>วันที่:</strong> {$booking_date}</p>
                    <p><strong>เวลา:</strong> {$booking_time}</p>
                    <p><strong>ราคา:</strong> {$booking['total_price']} บาท</p>
                    <p><strong>สถานะ:</strong> รอการยืนยัน</p>
                </div>
                
                <p><strong>หมายเหตุ:</strong> กรุณามาถึงก่อนเวลานัดหมาย 10 นาที</p>
                <p>หากต้องการยกเลิกหรือเปลี่ยนแปลงการจอง กรุณาติดต่อเรา</p>
            </div>
            
            <div class='footer'>
                <p>ร้านตัดผม Barber Shop<br>
                โทร: ************<br>
                อีเมล: <EMAIL></p>
            </div>
        </div>
    </body>
    </html>";
    
    return $html;
}

/**
 * สร้างเทมเพลตอีเมลสำหรับการอัพเดทสถานะ
 */
function createStatusUpdateEmailTemplate($booking, $status, $status_text) {
    $booking_date = formatDateThai($booking['booking_date']);
    $booking_time = formatTime12Hour($booking['booking_time']);
    
    $html = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .status-update { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .status-{$status} { border-left: 5px solid " . getStatusColor($status) . "; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>แจ้งเตือนสถานะการจอง</h1>
                <p>ร้านตัดผม Barber Shop</p>
            </div>
            
            <div class='content'>
                <p>เรียน คุณ{$booking['full_name']}</p>
                <p>สถานะการจองคิวของท่านมีการเปลี่ยนแปลง</p>
                
                <div class='status-update status-{$status}'>
                    <h3>รายละเอียดการจอง</h3>
                    <p><strong>หมายเลขการจอง:</strong> #{$booking['id']}</p>
                    <p><strong>บริการ:</strong> {$booking['service_name']}</p>
                    <p><strong>ช่างตัดผม:</strong> {$booking['barber_name']}</p>
                    <p><strong>วันที่:</strong> {$booking_date}</p>
                    <p><strong>เวลา:</strong> {$booking_time}</p>
                    <p><strong>สถานะใหม่:</strong> <span style='color: " . getStatusColor($status) . "; font-weight: bold;'>{$status_text}</span></p>
                </div>
                
                " . getStatusMessage($status) . "
            </div>
            
            <div class='footer'>
                <p>ร้านตัดผม Barber Shop<br>
                โทร: ************<br>
                อีเมล: <EMAIL></p>
            </div>
        </div>
    </body>
    </html>";
    
    return $html;
}

/**
 * ได้สีตามสถานะ
 */
function getStatusColor($status) {
    switch ($status) {
        case 'confirmed': return '#27ae60';
        case 'completed': return '#3498db';
        case 'cancelled': return '#e74c3c';
        default: return '#f39c12';
    }
}

/**
 * ได้ข้อความตามสถานะ
 */
function getStatusMessage($status) {
    switch ($status) {
        case 'confirmed':
            return "<p>การจองของท่านได้รับการยืนยันแล้ว กรุณามาถึงตามเวลานัดหมาย</p>";
        case 'completed':
            return "<p>ขอบคุณที่ใช้บริการของเรา หวังว่าท่านจะพอใจกับบริการ</p>";
        case 'cancelled':
            return "<p>การจองของท่านถูกยกเลิกแล้ว หากมีข้อสงสัยกรุณาติดต่อเรา</p>";
        default:
            return "";
    }
}
?>
