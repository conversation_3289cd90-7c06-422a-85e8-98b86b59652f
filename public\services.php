<?php
require_once __DIR__ . '/../includes/functions.php';

// ดึงข้อมูลบริการทั้งหมด
$services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY price ASC");
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>บริการ - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="promotion.php">โปรโมชั่น</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">แกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">เกี่ยวกับเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว</a></li>
                                    <li><a class="dropdown-item" href="../customer/my_bookings.php">การจองของฉัน</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 bg-primary text-white" style="margin-top: 76px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-cut"></i> บริการของเรา
                    </h1>
                    <p class="lead">บริการตัดผมคุณภาพสูง ด้วยช่างมืออาชีพ</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="py-5">
        <div class="container">
            <?php if (empty($services)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <h4>ยังไม่มีบริการ</h4>
                            <p>ขณะนี้ยังไม่มีบริการให้เลือก กรุณาติดต่อเราสำหรับข้อมูลเพิ่มเติม</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($services as $service): ?>
                        <div class="col-lg-4 col-md-6">
                            <?php if ($service['image']): ?>
                                <!-- แสดงแค่รูปภาพเมื่อมีรูป -->
                                <div class="position-relative overflow-hidden rounded-4 shadow-lg service-image-only"
                                     style="transition: all 0.4s ease;">
                                    <img src="../serve_image.php?image=<?= urlencode($service['image']) ?>"
                                         class="w-100"
                                         alt="<?= sanitize($service['name']) ?>"
                                         style="height: 400px; object-fit: cover; transition: transform 0.3s ease;"
                                         onmouseover="this.style.transform='scale(1.05)'"
                                         onmouseout="this.style.transform='scale(1)'">

                                    <!-- Overlay with service info -->
                                    <div class="position-absolute bottom-0 start-0 end-0 p-4"
                                         style="background: linear-gradient(transparent, rgba(0,0,0,0.8));">
                                        <h5 class="text-white fw-bold mb-2"><?= sanitize($service['name']) ?></h5>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="text-white">
                                                <i class="fas fa-clock me-1"></i><?= $service['duration'] ?> นาที
                                            </span>
                                            <span class="badge bg-light text-dark fs-6 px-3 py-2">
                                                <?= number_format($service['price']) ?> ฿
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Hover overlay with booking button -->
                                    <div class="position-absolute top-0 start-0 end-0 bottom-0 d-flex align-items-center justify-content-center opacity-0 service-hover-overlay"
                                         style="background: rgba(102, 126, 234, 0.9); transition: opacity 0.3s ease;">
                                        <div class="text-center text-white">
                                            <h5 class="mb-3"><?= sanitize($service['name']) ?></h5>
                                            <p class="mb-4"><?= sanitize($service['description']) ?></p>
                                            <?php if (isLoggedIn() && !isAdmin()): ?>
                                                <a href="../customer/book.php?service_id=<?= $service['id'] ?>"
                                                   class="btn btn-light btn-lg rounded-pill px-4">
                                                    <i class="fas fa-calendar-plus me-2"></i>จองบริการนี้
                                                </a>
                                            <?php elseif (!isLoggedIn()): ?>
                                                <a href="login.php" class="btn btn-outline-light btn-lg rounded-pill px-4">
                                                    <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบเพื่อจอง
                                                </a>
                                            <?php else: ?>
                                                <button class="btn btn-secondary btn-lg rounded-pill px-4" disabled>
                                                    <i class="fas fa-user-shield me-2"></i>สำหรับลูกค้าเท่านั้น
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- แสดง card ปกติเมื่อไม่มีรูป -->
                                <div class="card h-100 shadow-lg service-card border-0 overflow-hidden">
                                    <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center"
                                         style="height: 280px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                        <i class="fas fa-cut fa-5x text-white opacity-75"></i>
                                    </div>

                                    <div class="card-body d-flex flex-column p-4">
                                        <h5 class="card-title text-dark fw-bold mb-3" style="font-size: 1.4rem;">
                                            <?= sanitize($service['name']) ?>
                                        </h5>
                                        <p class="card-text text-muted flex-grow-1 mb-4" style="line-height: 1.6;">
                                            <?= sanitize($service['description']) ?>
                                        </p>

                                        <div class="service-details mt-auto">
                                            <div class="d-flex align-items-center justify-content-between mb-4">
                                                <div class="bg-light rounded-pill px-3 py-2 d-inline-flex align-items-center">
                                                    <i class="fas fa-clock text-primary me-2"></i>
                                                    <span class="fw-semibold text-dark"><?= $service['duration'] ?> นาที</span>
                                                </div>
                                                <span class="h5 text-primary fw-bold mb-0">
                                                    <?= number_format($service['price']) ?> ฿
                                                </span>
                                            </div>

                                            <div class="d-grid">
                                                <?php if (isLoggedIn() && !isAdmin()): ?>
                                                    <a href="../customer/book.php?service_id=<?= $service['id'] ?>"
                                                       class="btn btn-primary btn-lg rounded-pill shadow-sm"
                                                       style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                                                        <i class="fas fa-calendar-plus me-2"></i>จองบริการนี้
                                                    </a>
                                                <?php elseif (!isLoggedIn()): ?>
                                                    <a href="login.php" class="btn btn-outline-primary btn-lg rounded-pill">
                                                        <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบเพื่อจอง
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary btn-lg rounded-pill" disabled>
                                                        <i class="fas fa-user-shield me-2"></i>สำหรับลูกค้าเท่านั้น
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Service Features -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="text-center mb-4">จุดเด่นของบริการเรา</h3>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-3 text-center">
                        <div class="feature-box p-4">
                            <i class="fas fa-user-tie fa-3x text-primary mb-3"></i>
                            <h5>ช่างมืออาชีพ</h5>
                            <p class="text-muted">ช่างที่มีประสบการณ์และผ่านการฝึกอบรม</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="feature-box p-4">
                            <i class="fas fa-tools fa-3x text-success mb-3"></i>
                            <h5>เครื่องมือทันสมัย</h5>
                            <p class="text-muted">ใช้เครื่องมือคุณภาพสูงและทันสมัย</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="feature-box p-4">
                            <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                            <h5>สะอาดปลอดภัย</h5>
                            <p class="text-muted">ทำความสะอาดและฆ่าเชื้อทุกครั้ง</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3 text-center">
                        <div class="feature-box p-4">
                            <i class="fas fa-clock fa-3x text-info mb-3"></i>
                            <h5>ตรงเวลา</h5>
                            <p class="text-muted">บริการตรงเวลาตามที่นัดหมาย</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4">พร้อมจองคิวแล้วหรือยัง?</h3>
                    <p class="lead text-muted mb-4">
                        จองคิวออนไลน์ง่ายๆ ไม่ต้องรอนาน เลือกเวลาที่สะดวกสำหรับคุณ
                    </p>
                    
                    <?php if (isLoggedIn() && !isAdmin()): ?>
                        <a href="../customer/book.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-calendar-plus"></i> จองคิวเลย
                        </a>
                        <a href="../customer/my_bookings.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-list"></i> ดูการจองของฉัน
                        </a>
                    <?php elseif (!isLoggedIn()): ?>
                        <a href="register.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-user-plus"></i> สมัครสมาชิก
                        </a>
                        <a href="login.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-cut"></i> Barber Shop</h5>
                    <p>ร้านตัดผมมืออาชีพ ให้บริการด้วยใจ มีคุณภาพ</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                </div>
                <div class="col-md-4">
                    <h5>เวลาทำการ</h5>
                    <p>จันทร์ - เสาร์: 09:00 - 20:00</p>
                    <p>อาทิตย์: 10:00 - 18:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Barber Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
