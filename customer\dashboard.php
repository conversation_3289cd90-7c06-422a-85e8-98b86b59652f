<?php
require_once __DIR__ . '/../includes/auth.php';
requireCustomer();

// ดึงข้อมูลสถิติของลูกค้า
$user_id = $_SESSION['user_id'];

// จำนวนการจองทั้งหมด
$total_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ?", [$user_id])['count'];

// การจองที่รอยืนยัน
$pending_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'pending'", [$user_id])['count'];

// การจองที่ยืนยันแล้ว
$confirmed_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'confirmed'", [$user_id])['count'];

// การจองล่าสุด 5 รายการ
$recent_bookings = fetchAll("
    SELECT b.*, s.name as service_name, br.name as barber_name 
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    WHERE b.user_id = ?
    ORDER BY b.created_at DESC
    LIMIT 5
", [$user_id]);

// การจองที่จะมาถึง
$upcoming_bookings = fetchAll("
    SELECT b.*, s.name as service_name, br.name as barber_name 
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    WHERE b.user_id = ? AND b.booking_date >= CURDATE() AND b.status IN ('pending', 'confirmed')
    ORDER BY b.booking_date ASC, b.booking_time ASC
    LIMIT 3
", [$user_id]);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book.php">จองคิว</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my_bookings.php">การจองของฉัน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../public/services.php">บริการ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h2 class="mb-3">
                        <i class="fas fa-home text-primary"></i> 
                        ยินดีต้อนรับ, <?= sanitize($_SESSION['full_name']) ?>
                    </h2>
                    <p class="text-muted">จัดการการจองคิวและดูข้อมูลของคุณได้ที่นี่</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="dashboard-card stat-card bg-primary text-white">
                    <i class="fas fa-calendar-alt fa-2x mb-3"></i>
                    <div class="stat-number"><?= $total_bookings ?></div>
                    <div class="stat-label">การจองทั้งหมด</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card stat-card bg-warning text-white">
                    <i class="fas fa-clock fa-2x mb-3"></i>
                    <div class="stat-number"><?= $pending_bookings ?></div>
                    <div class="stat-label">รอยืนยัน</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="dashboard-card stat-card bg-success text-white">
                    <i class="fas fa-check-circle fa-2x mb-3"></i>
                    <div class="stat-number"><?= $confirmed_bookings ?></div>
                    <div class="stat-label">ยืนยันแล้ว</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Upcoming Bookings -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-calendar-check text-success"></i> 
                        การจองที่จะมาถึง
                    </h5>
                    
                    <?php if (empty($upcoming_bookings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">ไม่มีการจองที่จะมาถึง</p>
                            <a href="book.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> จองคิวใหม่
                            </a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($upcoming_bookings as $booking): ?>
                            <div class="border-start border-3 border-primary ps-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?= sanitize($booking['service_name']) ?></h6>
                                        <p class="mb-1 text-muted">
                                            <i class="fas fa-user"></i> <?= sanitize($booking['barber_name']) ?>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-calendar"></i> 
                                            <?= formatDateThai($booking['booking_date']) ?>
                                        </p>
                                        <p class="mb-0">
                                            <i class="fas fa-clock"></i> 
                                            <?= formatTime12Hour($booking['booking_time']) ?>
                                        </p>
                                    </div>
                                    <span class="badge status-<?= $booking['status'] ?>">
                                        <?php
                                        $status_text = [
                                            'pending' => 'รอยืนยัน',
                                            'confirmed' => 'ยืนยันแล้ว',
                                            'completed' => 'เสร็จสิ้น',
                                            'cancelled' => 'ยกเลิก'
                                        ];
                                        echo $status_text[$booking['status']];
                                        ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center mt-3">
                            <a href="my_bookings.php" class="btn btn-outline-primary">
                                ดูการจองทั้งหมด
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt text-warning"></i> 
                        การดำเนินการด่วน
                    </h5>
                    
                    <div class="d-grid gap-3">
                        <a href="book.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar-plus"></i> จองคิวใหม่
                        </a>
                        
                        <a href="my_bookings.php" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> ดูการจองของฉัน
                        </a>
                        
                        <a href="../public/services.php" class="btn btn-outline-info">
                            <i class="fas fa-cut"></i> ดูบริการทั้งหมด
                        </a>
                        
                        <a href="../public/promotion.php" class="btn btn-outline-success">
                            <i class="fas fa-tags"></i> โปรโมชั่น
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <?php if (!empty($recent_bookings)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-history text-info"></i> 
                        การจองล่าสุด
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>วันที่จอง</th>
                                    <th>บริการ</th>
                                    <th>ช่างตัดผม</th>
                                    <th>วันที่นัด</th>
                                    <th>เวลา</th>
                                    <th>สถานะ</th>
                                    <th>ราคา</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_bookings as $booking): ?>
                                <tr>
                                    <td><?= date('d/m/Y', strtotime($booking['created_at'])) ?></td>
                                    <td><?= sanitize($booking['service_name']) ?></td>
                                    <td><?= sanitize($booking['barber_name']) ?></td>
                                    <td><?= formatDateThai($booking['booking_date']) ?></td>
                                    <td><?= formatTime12Hour($booking['booking_time']) ?></td>
                                    <td>
                                        <span class="badge status-<?= $booking['status'] ?>">
                                            <?php
                                            $status_text = [
                                                'pending' => 'รอยืนยัน',
                                                'confirmed' => 'ยืนยันแล้ว',
                                                'completed' => 'เสร็จสิ้น',
                                                'cancelled' => 'ยกเลิก'
                                            ];
                                            echo $status_text[$booking['status']];
                                            ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($booking['total_price']) ?> บาท</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
</body>
</html>
