<?php
/**
 * ระบบแจ้งเตือนสำหรับแอดมิน
 */

require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'send_notification') {
        $title = trim($_POST['title'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $type = $_POST['type'] ?? 'info';
        $target = $_POST['target'] ?? 'all';
        
        if (empty($title) || empty($message)) {
            $error_message = 'กรุณากรอกหัวข้อและข้อความ';
        } else {
            // บันทึกการแจ้งเตือน
            $sql = "INSERT INTO notifications (title, message, type, target_audience, created_by, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
            $result = executeQuery($sql, [$title, $message, $type, $target, $_SESSION['user_id']]);
            
            if ($result) {
                $success_message = 'ส่งการแจ้งเตือนสำเร็จ';
                
                // ส่งอีเมลหรือ Line Notify (ถ้าต้องการ)
                if ($target === 'all' || $target === 'customers') {
                    // ส่งให้ลูกค้าทั้งหมด
                    $customers = fetchAll("SELECT email FROM users WHERE role = 'customer' AND email IS NOT NULL");
                    foreach ($customers as $customer) {
                        // ส่งอีเมล (ถ้ามีระบบอีเมล)
                        // sendEmail($customer['email'], $title, $message);
                    }
                }
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการส่งการแจ้งเตือน';
            }
        }
    }
}

// ตรวจสอบว่าตาราง notifications มีอยู่หรือไม่
$table_exists = fetchOne("SHOW TABLES LIKE 'notifications'");

if ($table_exists) {
    // ดึงการแจ้งเตือนล่าสุด
    $notifications = fetchAll("
        SELECT n.*, u.full_name as created_by_name
        FROM notifications n
        LEFT JOIN users u ON n.created_by = u.id
        ORDER BY n.created_at DESC
        LIMIT 50
    ");

    // สถิติการแจ้งเตือน
    $stats = fetchOne("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warning_count,
            SUM(CASE WHEN type = 'danger' THEN 1 ELSE 0 END) as danger_count,
            SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info_count
        FROM notifications
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
} else {
    // ถ้าไม่มีตาราง ให้ใช้ค่าเริ่มต้น
    $notifications = [];
    $stats = [
        'total' => 0,
        'success_count' => 0,
        'warning_count' => 0,
        'danger_count' => 0,
        'info_count' => 0
    ];
}

// ตรวจสอบว่า $stats เป็น array หรือไม่
if (!is_array($stats)) {
    $stats = [
        'total' => 0,
        'success_count' => 0,
        'warning_count' => 0,
        'danger_count' => 0,
        'info_count' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบแจ้งเตือน - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-cut"></i> Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-arrow-left"></i> กลับแดชบอร์ด
                </a>
                <a class="nav-link" href="../public/logout.php">
                    <i class="fas fa-sign-out-alt"></i> ออกจากระบบ
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-bell text-primary"></i> ระบบแจ้งเตือน</h2>
                <p class="text-muted">จัดการการแจ้งเตือนและข้อความถึงผู้ใช้</p>
            </div>
        </div>

        <!-- Messages -->
        <?php if (!$table_exists): ?>
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>ตาราง notifications ยังไม่มี!</strong>
                กรุณารันไฟล์ SQL: <code>sql/add_notifications_table.sql</code> เพื่อสร้างตาราง
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-bell fa-2x mb-2"></i>
                        <h3><?= $stats['total'] ?></h3>
                        <p>การแจ้งเตือนทั้งหมด (30 วัน)</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?= $stats['success_count'] ?></h3>
                        <p>ข้อความสำเร็จ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h3><?= $stats['warning_count'] ?></h3>
                        <p>ข้อความเตือน</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x mb-2"></i>
                        <h3><?= $stats['danger_count'] ?></h3>
                        <p>ข้อความข้อผิดพลาด</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Send Notification Form -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-paper-plane"></i> ส่งการแจ้งเตือนใหม่</h5>
            </div>
            <div class="card-body">
                <?php if (!$table_exists): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        ระบบแจ้งเตือนยังไม่พร้อมใช้งาน กรุณาสร้างตารางก่อน
                    </div>
                <?php else: ?>
                <form method="POST">
                    <input type="hidden" name="action" value="send_notification">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="title" name="title" required>
                                <label for="title">หัวข้อการแจ้งเตือน</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="type" name="type">
                                    <option value="info">ข้อมูล</option>
                                    <option value="success">สำเร็จ</option>
                                    <option value="warning">เตือน</option>
                                    <option value="danger">ข้อผิดพลาด</option>
                                </select>
                                <label for="type">ประเภท</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="target" name="target">
                                    <option value="all">ทุกคน</option>
                                    <option value="customers">ลูกค้า</option>
                                    <option value="admins">แอดมิน</option>
                                </select>
                                <label for="target">กลุ่มเป้าหมาย</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <textarea class="form-control" id="message" name="message" style="height: 120px" required></textarea>
                        <label for="message">ข้อความ</label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> ส่งการแจ้งเตือน
                        </button>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>

        <!-- Notifications History -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> ประวัติการแจ้งเตือน</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>วันที่/เวลา</th>
                                <th>หัวข้อ</th>
                                <th>ข้อความ</th>
                                <th>ประเภท</th>
                                <th>กลุ่มเป้าหมาย</th>
                                <th>ผู้ส่ง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($notifications as $notification): ?>
                                <tr>
                                    <td>
                                        <small><?= date('d/m/Y H:i', strtotime($notification['created_at'])) ?></small>
                                    </td>
                                    <td><?= sanitize($notification['title']) ?></td>
                                    <td>
                                        <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                            <?= sanitize(substr($notification['message'], 0, 100)) ?>
                                            <?= strlen($notification['message']) > 100 ? '...' : '' ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $type_colors = [
                                            'success' => 'success',
                                            'warning' => 'warning', 
                                            'danger' => 'danger',
                                            'info' => 'info'
                                        ];
                                        $type_names = [
                                            'success' => 'สำเร็จ',
                                            'warning' => 'เตือน',
                                            'danger' => 'ข้อผิดพลาด',
                                            'info' => 'ข้อมูล'
                                        ];
                                        ?>
                                        <span class="badge bg-<?= $type_colors[$notification['type']] ?>">
                                            <?= $type_names[$notification['type']] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $target_names = [
                                            'all' => 'ทุกคน',
                                            'customers' => 'ลูกค้า',
                                            'admins' => 'แอดมิน'
                                        ];
                                        ?>
                                        <span class="badge bg-secondary">
                                            <?= $target_names[$notification['target_audience']] ?>
                                        </span>
                                    </td>
                                    <td><?= sanitize($notification['created_by_name']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
