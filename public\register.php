<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// ป้องกันการเข้าถึงหากเข้าสู่ระบบแล้ว
redirectIfLoggedIn();

$error_message = '';
$success_message = '';
$form_data = [];

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = trim($_POST['full_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // เก็บข้อมูลฟอร์มไว้แสดงกรณีมีข้อผิดพลาด
    $form_data = [
        'username' => $username,
        'email' => $email,
        'full_name' => $full_name,
        'phone' => $phone
    ];
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
    }
    // ตรวจสอบความยาวชื่อผู้ใช้
    elseif (strlen($username) < 3 || strlen($username) > 50) {
        $error_message = 'ชื่อผู้ใช้ต้องมีความยาว 3-50 ตัวอักษร';
    }
    // ตรวจสอบรูปแบบอีเมล
    elseif (!isValidEmail($email)) {
        $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
    }
    // ตรวจสอบความยาวรหัสผ่าน
    elseif (strlen($password) < 6) {
        $error_message = 'รหัสผ่านต้องมีความยาวอย่างน้อย 6 ตัวอักษร';
    }
    // ตรวจสอบการยืนยันรหัสผ่าน
    elseif ($password !== $confirm_password) {
        $error_message = 'รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน';
    }
    // ตรวจสอบรูปแบบเบอร์โทรศัพท์
    elseif (!empty($phone) && !isValidPhone($phone)) {
        $error_message = 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง';
    }
    else {
        // พยายามสมัครสมาชิก
        if (register($username, $email, $password, $full_name, $phone)) {
            // สมัครสมาชิกสำเร็จ
            header('Location: login.php?message=registered');
            exit;
        } else {
            $error_message = 'ชื่อผู้ใช้หรืออีเมลนี้มีอยู่ในระบบแล้ว';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สมัครสมาชิก - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="auth-card">
                        <div class="auth-header">
                            <h2><i class="fas fa-user-plus"></i> สมัครสมาชิก</h2>
                            <p class="mb-0">ร้านตัดผม Barber Shop</p>
                        </div>
                        
                        <div class="auth-body">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">
                                                <i class="fas fa-user"></i> ชื่อผู้ใช้ *
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="username" 
                                                   name="username" 
                                                   value="<?= sanitize($form_data['username'] ?? '') ?>"
                                                   minlength="3"
                                                   maxlength="50"
                                                   pattern="[a-zA-Z0-9_]+"
                                                   required>
                                            <div class="invalid-feedback">
                                                ชื่อผู้ใช้ต้องมี 3-50 ตัวอักษร (a-z, A-Z, 0-9, _)
                                            </div>
                                            <div class="form-text">
                                                ใช้ได้เฉพาะตัวอักษรภาษาอังกฤษ ตัวเลข และ _
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope"></i> อีเมล *
                                            </label>
                                            <input type="email" 
                                                   class="form-control" 
                                                   id="email" 
                                                   name="email" 
                                                   value="<?= sanitize($form_data['email'] ?? '') ?>"
                                                   required>
                                            <div class="invalid-feedback">
                                                กรุณากรอกอีเมลที่ถูกต้อง
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-id-card"></i> ชื่อ-นามสกุล *
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="full_name" 
                                           name="full_name" 
                                           value="<?= sanitize($form_data['full_name'] ?? '') ?>"
                                           maxlength="100"
                                           required>
                                    <div class="invalid-feedback">
                                        กรุณากรอกชื่อ-นามสกุล
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone"></i> เบอร์โทรศัพท์
                                    </label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="phone" 
                                           name="phone" 
                                           value="<?= sanitize($form_data['phone'] ?? '') ?>"
                                           pattern="[0-9\-\+\(\)\s]+"
                                           maxlength="20">
                                    <div class="invalid-feedback">
                                        รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง
                                    </div>
                                    <div class="form-text">
                                        ตัวอย่าง: ************
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock"></i> รหัสผ่าน *
                                            </label>
                                            <div class="input-group">
                                                <input type="password" 
                                                       class="form-control" 
                                                       id="password" 
                                                       name="password" 
                                                       minlength="6"
                                                       required>
                                                <button class="btn btn-outline-secondary" 
                                                        type="button" 
                                                        onclick="togglePassword('password')">
                                                    <i class="fas fa-eye" id="password-eye"></i>
                                                </button>
                                            </div>
                                            <div class="invalid-feedback">
                                                รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="confirm_password" class="form-label">
                                                <i class="fas fa-lock"></i> ยืนยันรหัสผ่าน *
                                            </label>
                                            <div class="input-group">
                                                <input type="password" 
                                                       class="form-control" 
                                                       id="confirm_password" 
                                                       name="confirm_password" 
                                                       minlength="6"
                                                       required>
                                                <button class="btn btn-outline-secondary" 
                                                        type="button" 
                                                        onclick="togglePassword('confirm_password')">
                                                    <i class="fas fa-eye" id="confirm_password-eye"></i>
                                                </button>
                                            </div>
                                            <div class="invalid-feedback" id="password-feedback">
                                                กรุณายืนยันรหัสผ่าน
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">
                                        ฉันยอมรับ <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">เงื่อนไขการใช้งาน</a> และ <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">นโยบายความเป็นส่วนตัว</a>
                                    </label>
                                    <div class="invalid-feedback">
                                        กรุณายอมรับเงื่อนไขการใช้งาน
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus"></i> สมัครสมาชิก
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">มีบัญชีอยู่แล้ว?</p>
                                <a href="login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                                </a>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="index.php" class="text-muted">
                                    <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        // ฟังก์ชันแสดง/ซ่อนรหัสผ่าน
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const eye = document.getElementById(inputId + '-eye');
            
            if (input.type === 'password') {
                input.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }
        
        // Focus ที่ input แรก
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
