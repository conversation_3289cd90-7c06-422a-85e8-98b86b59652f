<?php
/**
 * API สำหรับดึงข้อมูลบริการ
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';

// ตั้งค่า header สำหรับ JSON response
header('Content-Type: application/json');

// ตรวจสอบสิทธิ์ admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode([
        'success' => false,
        'message' => 'ไม่มีสิทธิ์เข้าถึง'
    ]);
    exit;
}

// ตรวจสอบ parameter
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'ไม่พบ ID บริการ',
        'debug' => [
            'get_params' => $_GET,
            'id_exists' => isset($_GET['id']),
            'id_numeric' => isset($_GET['id']) ? is_numeric($_GET['id']) : false
        ]
    ]);
    exit;
}

$service_id = intval($_GET['id']);

try {
    $service = fetchOne("SELECT * FROM services WHERE id = ?", [$service_id]);

    if ($service) {
        echo json_encode([
            'success' => true,
            'service' => [
                'id' => (int)$service['id'],
                'name' => $service['name'],
                'description' => $service['description'],
                'price' => (float)$service['price'],
                'duration' => (int)$service['duration'],
                'image' => $service['image'],
                'is_active' => (bool)$service['is_active'],
                'created_at' => $service['created_at'],
                'updated_at' => $service['updated_at']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ไม่พบบริการที่ต้องการ',
            'debug' => [
                'service_id' => $service_id,
                'query_result' => $service
            ]
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
