<?php
require_once __DIR__ . '/../includes/auth.php';
requireCustomer();

$error_message = '';
$success_message = '';

// ดึงข้อมูลบริการ
$services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");

// ดึงข้อมูลช่างตัดผม
$barbers = fetchAll("SELECT * FROM barbers WHERE is_active = 1 ORDER BY name");

// ดึงข้อมูลโปรโมชั่น
$promotions = fetchAll("SELECT * FROM promotions WHERE is_active = 1 AND start_date <= CURDATE() AND end_date >= CURDATE() ORDER BY title");

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $service_id = $_POST['service_id'] ?? '';
    $barber_id = $_POST['barber_id'] ?? '';
    $booking_date = $_POST['booking_date'] ?? '';
    $booking_time = $_POST['booking_time'] ?? '';
    $promotion_id = $_POST['promotion_id'] ?? null;
    $notes = trim($_POST['notes'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($service_id) || empty($barber_id) || empty($booking_date) || empty($booking_time)) {
        $error_message = 'กรุณากรอกข้อมูลให้ครบถ้วน';
    }
    // ตรวจสอบวันที่ (ต้องไม่เป็นวันที่ผ่านมาแล้ว)
    elseif ($booking_date < date('Y-m-d')) {
        $error_message = 'กรุณาเลือกวันที่ในอนาคต';
    }
    else {
        // ดึงข้อมูลบริการเพื่อคำนวณราคา
        $service = fetchOne("SELECT * FROM services WHERE id = ? AND is_active = 1", [$service_id]);
        
        if (!$service) {
            $error_message = 'ไม่พบบริการที่เลือก';
        } else {
            // ตรวจสอบว่าช่วงเวลาว่างหรือไม่
            if (!isTimeSlotAvailable($barber_id, $booking_date, $booking_time, $service['duration'])) {
                $error_message = 'ช่วงเวลาที่เลือกไม่ว่าง กรุณาเลือกเวลาอื่น';
            } else {
                // คำนวณราคา
                $total_price = calculateDiscountedPrice($service['price'], $promotion_id);
                
                // บันทึกการจอง
                $sql = "INSERT INTO bookings (user_id, service_id, barber_id, booking_date, booking_time, notes, total_price, status) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')";
                
                $result = executeQuery($sql, [
                    $_SESSION['user_id'],
                    $service_id,
                    $barber_id,
                    $booking_date,
                    $booking_time,
                    $notes,
                    $total_price
                ]);
                
                if ($result) {
                    $booking_id = getLastInsertId();
                    
                    // ส่งอีเมลแจ้งเตือน
                    sendBookingConfirmation($booking_id);
                    
                    $success_message = 'จองคิวสำเร็จ! เราจะติดต่อกลับเพื่อยืนยันการจอง';
                    
                    // Redirect เพื่อป้องกันการส่งฟอร์มซ้ำ
                    header("Location: booking_detail.php?id=$booking_id&success=1");
                    exit;
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการจองคิว กรุณาลองใหม่อีกครั้ง';
                }
            }
        }
    }
}

// ตรวจสอบ service_id จาก URL
$selected_service_id = $_GET['service_id'] ?? '';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จองคิว - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="book.php">จองคิว</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my_bookings.php">การจองของฉัน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../public/services.php">บริการ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="booking-form">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-calendar-plus text-primary"></i> จองคิว
                    </h2>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        <input type="hidden" id="booking_time" name="booking_time">
                        
                        <!-- เลือกบริการ -->
                        <div class="mb-4">
                            <label for="service_id" class="form-label">
                                <i class="fas fa-cut"></i> เลือกบริการ *
                            </label>
                            <select class="form-select" id="service_id" name="service_id" required>
                                <option value="">-- เลือกบริการ --</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>" 
                                            data-price="<?= $service['price'] ?>"
                                            data-duration="<?= $service['duration'] ?>"
                                            <?= $selected_service_id == $service['id'] ? 'selected' : '' ?>>
                                        <?= sanitize($service['name']) ?> - <?= number_format($service['price']) ?> บาท (<?= $service['duration'] ?> นาที)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">กรุณาเลือกบริการ</div>
                        </div>
                        
                        <!-- เลือกช่างตัดผม -->
                        <div class="mb-4">
                            <label for="barber_id" class="form-label">
                                <i class="fas fa-user-tie"></i> เลือกช่างตัดผม *
                            </label>
                            <select class="form-select" id="barber_id" name="barber_id" required>
                                <option value="">-- เลือกช่างตัดผม --</option>
                                <?php foreach ($barbers as $barber): ?>
                                    <option value="<?= $barber['id'] ?>">
                                        <?= sanitize($barber['name']) ?>
                                        <?php if ($barber['speciality']): ?>
                                            - <?= sanitize($barber['speciality']) ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">กรุณาเลือกช่างตัดผม</div>
                        </div>
                        
                        <!-- เลือกวันที่ -->
                        <div class="mb-4">
                            <label for="booking_date" class="form-label">
                                <i class="fas fa-calendar"></i> เลือกวันที่ *
                            </label>
                            <input type="date" class="form-control" id="booking_date" name="booking_date" required>
                            <div class="invalid-feedback">กรุณาเลือกวันที่</div>
                            <div class="form-text">สามารถจองล่วงหน้าได้สูงสุด 30 วัน</div>
                        </div>
                        
                        <!-- เลือกเวลา -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-clock"></i> เลือกเวลา *
                            </label>
                            <div id="time-slots-container">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                                    <p>กรุณาเลือกบริการ ช่างตัดผม และวันที่ก่อน</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- โปรโมชั่น -->
                        <?php if (!empty($promotions)): ?>
                        <div class="mb-4">
                            <label for="promotion_id" class="form-label">
                                <i class="fas fa-tags"></i> โปรโมชั่น (ไม่บังคับ)
                            </label>
                            <select class="form-select" id="promotion_id" name="promotion_id">
                                <option value="">-- ไม่ใช้โปรโมชั่น --</option>
                                <?php foreach ($promotions as $promotion): ?>
                                    <option value="<?= $promotion['id'] ?>" 
                                            data-discount="<?= $promotion['discount_percent'] ?>">
                                        <?= sanitize($promotion['title']) ?>
                                        <?php if ($promotion['discount_percent']): ?>
                                            - ลด <?= $promotion['discount_percent'] ?>%
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        
                        <!-- หมายเหตุ -->
                        <div class="mb-4">
                            <label for="notes" class="form-label">
                                <i class="fas fa-sticky-note"></i> หมายเหตุ (ไม่บังคับ)
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="ระบุความต้องการพิเศษหรือหมายเหตุเพิ่มเติม"></textarea>
                        </div>
                        
                        <!-- สรุปราคา -->
                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-calculator"></i> สรุปการจอง
                                    </h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>ราคารวม:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <span id="total-price" class="h5 text-primary">0 บาท</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ปุ่มจอง -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-calendar-check"></i> ยืนยันการจอง
                            </button>
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> กลับ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        // ฟังก์ชันโหลดช่วงเวลาที่ว่าง
        function loadAvailableTimeSlots() {
            const serviceId = document.getElementById('service_id')?.value;
            const barberId = document.getElementById('barber_id')?.value;
            const bookingDate = document.getElementById('booking_date')?.value;
            const container = document.getElementById('time-slots-container');

            if (!serviceId || !barberId || !bookingDate || !container) {
                if (container) {
                    container.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-info-circle fa-2x mb-3"></i><p>กรุณาเลือกบริการ ช่างตัดผม และวันที่ก่อน</p></div>';
                }
                return;
            }

            // แสดง loading
            container.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><small class="text-muted mt-2">กำลังโหลดช่วงเวลา...</small></div>';

            // ส่ง AJAX request
            fetch('ajax/get_available_times.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `service_id=${serviceId}&barber_id=${barberId}&date=${bookingDate}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    container.innerHTML = data.html;

                    // เพิ่ม event listener สำหรับการเลือกเวลา
                    const timeSlots = container.querySelectorAll('.time-slot:not(.unavailable)');
                    timeSlots.forEach(slot => {
                        slot.addEventListener('click', function() {
                            // ลบ active class จากทุก slot
                            timeSlots.forEach(s => s.classList.remove('active'));

                            // เพิ่ม active class ให้ slot ที่เลือก
                            this.classList.add('active');

                            // ตั้งค่า hidden input
                            document.getElementById('booking_time').value = this.dataset.time;

                            // อัพเดทราคา
                            updateTotalPrice();
                        });
                    });
                } else {
                    container.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
            });
        }

        // ฟังก์ชันอัพเดทราคารวม
        function updateTotalPrice() {
            const serviceSelect = document.getElementById('service_id');
            const promotionSelect = document.getElementById('promotion_id');
            const totalPriceElement = document.getElementById('total-price');

            if (!serviceSelect || !totalPriceElement) {
                return;
            }

            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            let price = parseFloat(selectedOption.dataset.price) || 0;

            // คำนวณส่วนลด
            if (promotionSelect && promotionSelect.value) {
                const promotionOption = promotionSelect.options[promotionSelect.selectedIndex];
                const discount = parseFloat(promotionOption.dataset.discount) || 0;

                if (discount > 0) {
                    price = price * (1 - discount / 100);
                }
            }

            totalPriceElement.textContent = Math.round(price).toLocaleString() + ' บาท';
        }

        document.addEventListener('DOMContentLoaded', function() {
            const serviceSelect = document.getElementById('service_id');
            const barberSelect = document.getElementById('barber_id');
            const dateInput = document.getElementById('booking_date');

            // ตั้งค่าวันที่ขั้นต่ำ (วันนี้)
            const today = new Date();
            dateInput.min = today.toISOString().split('T')[0];

            // ตั้งค่าวันที่สูงสุด (30 วันข้างหน้า)
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 30);
            dateInput.max = maxDate.toISOString().split('T')[0];

            // เมื่อเปลี่ยนค่าใดๆ ให้โหลดช่วงเวลาใหม่
            [serviceSelect, barberSelect, dateInput].forEach(element => {
                element.addEventListener('change', loadAvailableTimeSlots);
            });

            // เมื่อเปลี่ยนบริการให้อัพเดทราคา
            serviceSelect.addEventListener('change', updateTotalPrice);
            if (document.getElementById('promotion_id')) {
                document.getElementById('promotion_id').addEventListener('change', updateTotalPrice);
            }

            // อัพเดทราคาครั้งแรก
            updateTotalPrice();
        });
    </script>
</body>
</html>
