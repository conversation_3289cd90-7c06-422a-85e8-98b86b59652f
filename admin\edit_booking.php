<?php
/**
 * หน้าแก้ไขการจองสำหรับแอดมิน
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
requireAdmin();

$booking_id = intval($_GET['id'] ?? 0);
$success_message = '';
$error_message = '';

// ตรวจสอบ booking_id
if ($booking_id <= 0) {
    die('ID การจองไม่ถูกต้อง');
}

// ดึงข้อมูลการจอง
try {
    $booking = fetchOne("
        SELECT b.*, s.name as service_name, s.price as service_price,
               br.name as barber_name, u.full_name as user_name
        FROM bookings b
        LEFT JOIN services s ON b.service_id = s.id
        LEFT JOIN barbers br ON b.barber_id = br.id
        LEFT JOIN users u ON b.user_id = u.id
        WHERE b.id = ?
    ", [$booking_id]);

    if (!$booking) {
        die('ไม่พบข้อมูลการจอง ID: ' . $booking_id);
    }
} catch (Exception $e) {
    die('ข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage());
}

// ดึงข้อมูลบริการและช่าง
$services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");
$barbers = fetchAll("SELECT * FROM barbers WHERE is_active = 1 ORDER BY name");

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $service_id = intval($_POST['service_id'] ?? 0);
        $barber_id = intval($_POST['barber_id'] ?? 0);
        $booking_date = trim($_POST['booking_date'] ?? '');
        $booking_time = trim($_POST['booking_time'] ?? '');
        $customer_name = trim($_POST['customer_name'] ?? '');
        $customer_phone = trim($_POST['customer_phone'] ?? '');
        $customer_email = trim($_POST['customer_email'] ?? '');
        $notes = trim($_POST['notes'] ?? '');
        $status = trim($_POST['status'] ?? '');

        // ตรวจสอบข้อมูลที่จำเป็น
        if (empty($service_id) || empty($barber_id) || empty($booking_date) || empty($booking_time) || empty($customer_name) || empty($customer_phone)) {
            $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
        } else {
            // ตรวจสอบว่าเวลานี้ว่างหรือไม่ (ยกเว้นการจองปัจจุบัน)
            $existing = fetchOne("
                SELECT id FROM bookings
                WHERE barber_id = ? AND booking_date = ? AND booking_time = ?
                AND id != ? AND status NOT IN ('cancelled', 'completed')
            ", [$barber_id, $booking_date, $booking_time, $booking_id]);

            if ($existing) {
                $error_message = 'เวลานี้มีการจองอื่นแล้ว กรุณาเลือกเวลาอื่น';
            } else {
            // คำนวณราคาใหม่
            $service = fetchOne("SELECT price FROM services WHERE id = ?", [$service_id]);
            $total_price = $service['price'];
            
            // อัปเดตการจอง
            $sql = "UPDATE bookings SET
                    service_id = ?, barber_id = ?, booking_date = ?, booking_time = ?,
                    customer_name = ?, customer_phone = ?, customer_email = ?,
                    notes = ?, total_price = ?, status = ?, updated_at = NOW()
                    WHERE id = ?";

            $params = [
                $service_id, $barber_id, $booking_date, $booking_time,
                $customer_name, $customer_phone, $customer_email,
                $notes, $total_price, $status, $booking_id
            ];

            $result = executeQuery($sql, $params);
            
            if ($result) {
                $success_message = 'แก้ไขการจองสำเร็จ';
                // รีเฟรชข้อมูล
                $booking = fetchOne("
                    SELECT b.*, s.name as service_name, s.price as service_price,
                           br.name as barber_name, u.full_name as user_name
                    FROM bookings b
                    LEFT JOIN services s ON b.service_id = s.id
                    LEFT JOIN barbers br ON b.barber_id = br.id
                    LEFT JOIN users u ON b.user_id = u.id
                    WHERE b.id = ?
                ", [$booking_id]);
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการแก้ไข: ไม่สามารถอัปเดตข้อมูลได้';
            }
            }
        }
    } catch (Exception $e) {
        $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขการจอง #<?= $booking['id'] ?> - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h3><i class="fas fa-edit text-warning"></i> แก้ไขการจอง #<?= $booking['id'] ?></h3>
                <p class="text-muted">แก้ไขข้อมูลการจองของลูกค้า</p>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Debug Info (แสดงเฉพาะเมื่อมี POST) -->
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST)): ?>
            <div class="alert alert-info">
                <h6>ข้อมูลที่ส่งมา:</h6>
                <small>
                    Service ID: <?= $_POST['service_id'] ?? 'ไม่มี' ?><br>
                    Barber ID: <?= $_POST['barber_id'] ?? 'ไม่มี' ?><br>
                    Date: <?= $_POST['booking_date'] ?? 'ไม่มี' ?><br>
                    Time: <?= $_POST['booking_time'] ?? 'ไม่มี' ?><br>
                    Status: <?= $_POST['status'] ?? 'ไม่มี' ?>
                </small>
            </div>
        <?php endif; ?>

        <!-- Edit Form -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-edit"></i> ฟอร์มแก้ไขการจอง</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- ข้อมูลลูกค้า -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-user"></i> ข้อมูลลูกค้า</h6>
                            
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="customer_name" name="customer_name"
                                       value="<?= htmlspecialchars($booking['customer_name'] ?? '') ?>" required>
                                <label for="customer_name">ชื่อ-นามสกุล</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone"
                                       value="<?= htmlspecialchars($booking['customer_phone'] ?? '') ?>" required>
                                <label for="customer_phone">เบอร์โทรศัพท์</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="customer_email" name="customer_email"
                                       value="<?= htmlspecialchars($booking['customer_email'] ?? '') ?>">
                                <label for="customer_email">อีเมล</label>
                            </div>
                        </div>
                        
                        <!-- ข้อมูลการจอง -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-calendar"></i> ข้อมูลการจอง</h6>
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" id="service_id" name="service_id" required>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?= $service['id'] ?>"
                                                <?= $service['id'] == ($booking['service_id'] ?? 0) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($service['name']) ?> - <?= number_format($service['price']) ?> บาท
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label for="service_id">บริการ</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <select class="form-select" id="barber_id" name="barber_id" required>
                                    <?php foreach ($barbers as $barber): ?>
                                        <option value="<?= $barber['id'] ?>"
                                                <?= $barber['id'] == ($booking['barber_id'] ?? 0) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($barber['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label for="barber_id">ช่างตัดผม</label>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="date" class="form-control" id="booking_date" name="booking_date"
                                               value="<?= htmlspecialchars($booking['booking_date'] ?? '') ?>" required>
                                        <label for="booking_date">วันที่จอง</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" id="booking_time" name="booking_time" required>
                                            <?php
                                            $times = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', 
                                                     '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', 
                                                     '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'];
                                            foreach ($times as $time):
                                            ?>
                                                <option value="<?= $time ?>"
                                                        <?= $time == ($booking['booking_time'] ?? '') ? 'selected' : '' ?>>
                                                    <?= $time ?> น.
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <label for="booking_time">เวลาจอง</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="status" name="status" required>
                                    <option value="pending" <?= ($booking['status'] ?? '') == 'pending' ? 'selected' : '' ?>>รอยืนยัน</option>
                                    <option value="confirmed" <?= ($booking['status'] ?? '') == 'confirmed' ? 'selected' : '' ?>>ยืนยันแล้ว</option>
                                    <option value="completed" <?= ($booking['status'] ?? '') == 'completed' ? 'selected' : '' ?>>เสร็จสิ้น</option>
                                    <option value="cancelled" <?= ($booking['status'] ?? '') == 'cancelled' ? 'selected' : '' ?>>ยกเลิก</option>
                                </select>
                                <label for="status">สถานะ</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="notes" name="notes" style="height: 100px"><?= htmlspecialchars($booking['notes'] ?? '') ?></textarea>
                                <label for="notes">หมายเหตุ</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> บันทึกการแก้ไข
                        </button>
                        <a href="../customer/booking_detail.php?id=<?= $booking['id'] ?>" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i> ดูรายละเอียด
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.close()">
                            <i class="fas fa-times"></i> ปิด
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Current Info -->
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-info-circle"></i> ข้อมูลปัจจุบัน</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>ลูกค้า:</strong> <?= htmlspecialchars($booking['customer_name'] ?? '') ?></p>
                        <p><strong>เบอร์:</strong> <?= htmlspecialchars($booking['customer_phone'] ?? '') ?></p>
                        <p><strong>บริการ:</strong> <?= htmlspecialchars($booking['service_name'] ?? '') ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>ช่าง:</strong> <?= htmlspecialchars($booking['barber_name'] ?? '') ?></p>
                        <p><strong>วันเวลา:</strong> <?= date('d/m/Y', strtotime($booking['booking_date'])) ?> เวลา <?= $booking['booking_time'] ?> น.</p>
                        <p><strong>ราคา:</strong> <?= number_format($booking['total_price'] ?? 0) ?> บาท</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-hide alerts
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    alert.classList.add('fade');
                    setTimeout(() => alert.remove(), 150);
                }
            });
        }, 5000);
    </script>
</body>
</html>
