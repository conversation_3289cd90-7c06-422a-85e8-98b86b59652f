/**
 * ไฟล์ JavaScript หลักสำหรับระบบจองคิวร้านตัดผม
 */

// เมื่อโหลดหน้าเสร็จ
document.addEventListener('DOMContentLoaded', function() {
    // เริ่มต้นฟังก์ชันต่างๆ
    initFormValidation();
    initTimeSlotSelection();
    initDatePicker();
    initConfirmDialogs();
    initTooltips();
    
    // ซ่อน alert หลังจาก 5 วินาที
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 500);
        });
    }, 5000);
});

/**
 * ฟังก์ชันตรวจสอบความถูกต้องของฟอร์ม
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * ฟังก์ชันเลือกช่วงเวลา
 */
function initTimeSlotSelection() {
    const timeSlots = document.querySelectorAll('.time-slot');
    const timeInput = document.getElementById('booking_time');
    
    timeSlots.forEach(function(slot) {
        slot.addEventListener('click', function() {
            if (!slot.classList.contains('unavailable')) {
                // ลบ class selected จากทุก slot
                timeSlots.forEach(function(s) {
                    s.classList.remove('selected');
                });
                
                // เพิ่ม class selected ให้ slot ที่เลือก
                slot.classList.add('selected');
                
                // ตั้งค่าใน input hidden
                if (timeInput) {
                    timeInput.value = slot.dataset.time;
                }
            }
        });
    });
}

/**
 * ฟังก์ชันสำหรับการจองคิว - โหลดช่วงเวลาที่ว่าง
 */
function loadAvailableTimeSlots() {
    const serviceId = document.getElementById('service_id')?.value;
    const barberId = document.getElementById('barber_id')?.value;
    const bookingDate = document.getElementById('booking_date')?.value;
    const container = document.getElementById('time-slots-container');

    if (!serviceId || !barberId || !bookingDate || !container) {
        return;
    }

    // แสดง loading
    container.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><small class="text-muted mt-2">กำลังโหลดช่วงเวลา...</small></div>';

    // ส่ง AJAX request
    fetch('ajax/get_available_times.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `service_id=${serviceId}&barber_id=${barberId}&date=${bookingDate}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            container.innerHTML = data.html;

            // เพิ่ม event listener สำหรับการเลือกเวลา
            const timeSlots = container.querySelectorAll('.time-slot:not(.unavailable)');
            timeSlots.forEach(slot => {
                slot.addEventListener('click', function() {
                    // ลบ active class จากทุก slot
                    timeSlots.forEach(s => s.classList.remove('active'));

                    // เพิ่ม active class ให้ slot ที่เลือก
                    this.classList.add('active');

                    // ตั้งค่า hidden input
                    document.getElementById('booking_time').value = this.dataset.time;

                    // อัพเดทราคา
                    updateTotalPrice();
                });
            });
        } else {
            container.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${data.message}</div>`;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        container.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
    });
}

/**
 * ฟังก์ชันอัพเดทราคารวม
 */
function updateTotalPrice() {
    const serviceSelect = document.getElementById('service_id');
    const promotionSelect = document.getElementById('promotion_id');
    const totalPriceElement = document.getElementById('total-price');

    if (!serviceSelect || !totalPriceElement) {
        return;
    }

    const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
    let price = parseFloat(selectedOption.dataset.price) || 0;

    // คำนวณส่วนลด
    if (promotionSelect && promotionSelect.value) {
        const promotionOption = promotionSelect.options[promotionSelect.selectedIndex];
        const discount = parseFloat(promotionOption.dataset.discount) || 0;

        if (discount > 0) {
            price = price * (1 - discount / 100);
        }
    }

    totalPriceElement.textContent = Math.round(price).toLocaleString() + ' บาท';
}

/**
 * ฟังก์ชัน Date Picker
 */
function initDatePicker() {
    const dateInput = document.getElementById('booking_date');

    if (dateInput) {
        // ตั้งค่าวันที่ขั้นต่ำเป็นวันนี้
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        dateInput.min = tomorrow.toISOString().split('T')[0];

        // เมื่อเปลี่ยนวันที่ให้โหลดช่วงเวลาใหม่
        dateInput.addEventListener('change', function() {
            loadAvailableTimeSlots();
        });
    }
}

/**
 * โหลดช่วงเวลาที่ว่าง
 */
function loadAvailableTimeSlots() {
    const dateInput = document.getElementById('booking_date');
    const barberSelect = document.getElementById('barber_id');
    const timeSlotsContainer = document.getElementById('time-slots-container');
    
    if (!dateInput.value || !barberSelect.value) {
        return;
    }
    
    // แสดง loading
    if (timeSlotsContainer) {
        timeSlotsContainer.innerHTML = '<div class="spinner"></div>';
    }
    
    // ส่ง AJAX request
    fetch('ajax/get_available_times.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `date=${dateInput.value}&barber_id=${barberSelect.value}`
    })
    .then(response => response.json())
    .then(data => {
        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = data.html;
            initTimeSlotSelection(); // เริ่มต้นฟังก์ชันเลือกเวลาใหม่
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = '<div class="alert alert-danger">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
        }
    });
}

/**
 * ฟังก์ชันยืนยันการกระทำ
 */
function initConfirmDialogs() {
    const confirmButtons = document.querySelectorAll('[data-confirm]');
    
    confirmButtons.forEach(function(button) {
        button.addEventListener('click', function(event) {
            const message = button.dataset.confirm;
            if (!confirm(message)) {
                event.preventDefault();
            }
        });
    });
}

/**
 * เริ่มต้น Tooltips
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * ฟังก์ชันตรวจสอบรหัสผ่าน
 */
function validatePassword() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const feedback = document.getElementById('password-feedback');
    
    if (password && confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            if (password.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('รหัสผ่านไม่ตรงกัน');
                if (feedback) {
                    feedback.textContent = 'รหัสผ่านไม่ตรงกัน';
                    feedback.className = 'invalid-feedback';
                }
            } else {
                confirmPassword.setCustomValidity('');
                if (feedback) {
                    feedback.textContent = 'รหัสผ่านตรงกัน';
                    feedback.className = 'valid-feedback';
                }
            }
        });
    }
}

/**
 * ฟังก์ชันค้นหาในตาราง
 */
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    
    if (input && table) {
        input.addEventListener('keyup', function() {
            const filter = input.value.toLowerCase();
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) { // เริ่มจาก 1 เพื่อข้าม header
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        });
    }
}

/**
 * ฟังก์ชันอัพเดทสถานะการจอง
 */
function updateBookingStatus(bookingId, status) {
    if (!confirm('คุณแน่ใจหรือไม่ที่จะเปลี่ยนสถานะ?')) {
        return;
    }
    
    fetch('ajax/update_booking_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `booking_id=${bookingId}&status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการอัพเดท');
    });
}

/**
 * ฟังก์ชันแสดงรายละเอียดการจอง
 */
function showBookingDetails(bookingId) {
    fetch(`ajax/get_booking_details.php?id=${bookingId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = document.getElementById('bookingModal');
            const modalBody = modal.querySelector('.modal-body');
            modalBody.innerHTML = data.html;
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } else {
            alert('ไม่สามารถโหลดข้อมูลได้');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
    });
}

/**
 * ฟังก์ชันคำนวณราคารวม
 */
function calculateTotalPrice() {
    const serviceSelect = document.getElementById('service_id');
    const promotionSelect = document.getElementById('promotion_id');
    const totalPriceElement = document.getElementById('total-price');
    
    if (!serviceSelect || !totalPriceElement) {
        return;
    }
    
    const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
    let price = parseFloat(selectedOption.dataset.price) || 0;
    
    // คำนวณส่วนลด
    if (promotionSelect && promotionSelect.value) {
        const promotionOption = promotionSelect.options[promotionSelect.selectedIndex];
        const discountPercent = parseFloat(promotionOption.dataset.discount) || 0;
        
        if (discountPercent > 0) {
            price = price * (1 - discountPercent / 100);
        }
    }
    
    totalPriceElement.textContent = price.toLocaleString('th-TH') + ' บาท';
}

/**
 * ฟังก์ชันโหลดข้อมูลแบบ AJAX
 */
function loadData(url, containerId, showLoading = true) {
    const container = document.getElementById(containerId);
    
    if (!container) {
        return;
    }
    
    if (showLoading) {
        container.innerHTML = '<div class="spinner"></div>';
    }
    
    fetch(url)
    .then(response => response.text())
    .then(html => {
        container.innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        container.innerHTML = '<div class="alert alert-danger">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
    });
}

/**
 * ฟังก์ชันส่งฟอร์มแบบ AJAX
 */
function submitFormAjax(formId, successCallback) {
    const form = document.getElementById(formId);
    
    if (!form) {
        return;
    }
    
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (successCallback) {
                    successCallback(data);
                } else {
                    location.reload();
                }
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการส่งข้อมูล');
        });
    });
}

// เรียกใช้ฟังก์ชันเมื่อโหลดหน้า
document.addEventListener('DOMContentLoaded', function() {
    validatePassword();
    
    // เริ่มต้นการค้นหาในตาราง
    searchTable('search-users', 'users-table');
    searchTable('search-bookings', 'bookings-table');
    searchTable('search-services', 'services-table');
    
    // เริ่มต้นการคำนวณราคา
    const serviceSelect = document.getElementById('service_id');
    const promotionSelect = document.getElementById('promotion_id');
    
    if (serviceSelect) {
        serviceSelect.addEventListener('change', calculateTotalPrice);
    }
    
    if (promotionSelect) {
        promotionSelect.addEventListener('change', calculateTotalPrice);
    }
    
    // คำนวณราคาครั้งแรก
    calculateTotalPrice();
});
