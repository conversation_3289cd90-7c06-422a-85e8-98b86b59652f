-- ฐานข้อมูลระบบจองคิวร้านตัดผม
-- =====================================================
-- โครงสร้างฐานข้อมูลระบบจองคิวร้านตัดผม
-- Database: barber_booking
-- Character Set: utf8mb4
-- Collation: utf8mb4_unicode_ci
-- =====================================================

-- สร้างฐานข้อมูล
CREATE DATABASE IF NOT EXISTS barber_booking CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE barber_booking;

-- ตารางผู้ใช้งาน
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('customer', 'admin') DEFAULT 'customer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ตารางช่างตัดผม
CREATE TABLE barbers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    speciality TEXT,
    image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ตารางบริการ (รองรับการอัปโหลดรูปภาพ)
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'ชื่อบริการ',
    description TEXT COMMENT 'คำอธิบายบริการ',
    price DECIMAL(10,2) NOT NULL COMMENT 'ราคาบริการ',
    duration INT NOT NULL COMMENT 'ระยะเวลาในหน่วยนาที',
    image VARCHAR(255) COMMENT 'ชื่อไฟล์รูปภาพบริการ (สำหรับ manage_services.php)',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการเปิด/ปิดบริการ',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไขล่าสุด'
);

-- ตารางการจองคิว
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    barber_id INT NOT NULL,
    service_id INT NOT NULL,
    booking_date DATE NOT NULL,
    booking_time TIME NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (barber_id) REFERENCES barbers(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- ตารางโปรโมชั่น
CREATE TABLE promotions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    discount_percent DECIMAL(5,2),
    discount_amount DECIMAL(10,2),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ตารางแกลเลอรี่
CREATE TABLE gallery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT 'ชื่อรูปภาพ',
    description TEXT COMMENT 'คำอธิบายรูปภาพ',
    image VARCHAR(255) NOT NULL COMMENT 'ชื่อไฟล์รูปภาพ',
    category VARCHAR(50) DEFAULT 'general' COMMENT 'หมวดหมู่รูปภาพ',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการแสดงผล',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT 'ตารางเก็บรูปภาพแกลเลอรี่';

-- ข้อมูลเริ่มต้น
-- ผู้ดูแลระบบ
INSERT INTO users (username, email, password, full_name, phone, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ผู้ดูแลระบบ', '************', 'admin');

-- ช่างตัดผม
INSERT INTO barbers (name, phone, email, speciality) VALUES
('นายสมชาย ใจดี', '************', '<EMAIL>', 'ตัดผมทรงคลาสสิก, โกนหนวด'),
('นายสมศักดิ์ มือทอง', '************', '<EMAIL>', 'ตัดผมทรงสมัยใหม่, สไตล์เกาหลี'),
('นายประยุทธ ช่างฝีมือ', '************', '<EMAIL>', 'ตัดผมเด็ก, ผู้สูงอายุ');

-- บริการ
INSERT INTO services (name, description, price, duration) VALUES
('ตัดผมธรรมดา', 'ตัดผมทรงพื้นฐาน สำหรับทุกเพศทุกวัย', 150.00, 30),
('ตัดผม + สระ', 'ตัดผมพร้อมสระผมด้วยแชมพูคุณภาพ', 200.00, 45),
('ตัดผม + โกนหนวด', 'ตัดผมและโกนหนวดแบบดั้งเดิม', 250.00, 60),
('สไตล์เกาหลี', 'ตัดผมทรงสมัยใหม่สไตล์เกาหลี', 300.00, 60),
('ตัดผมเด็ก', 'ตัดผมสำหรับเด็กอายุต่ำกว่า 12 ปี', 120.00, 25),
('โกนหนวดอย่างเดียว', 'โกนหนวดด้วยมีดโกนแบบดั้งเดิม', 100.00, 20);

-- โปรโมชั่น
INSERT INTO promotions (title, description, discount_percent, start_date, end_date) VALUES
('ลูกค้าใหม่ลด 20%', 'สำหรับลูกค้าใหม่ทุกท่าน รับส่วนลด 20% สำหรับการตัดผมครั้งแรก', 20.00, '2024-01-01', '2024-12-31'),
('Happy Hour ลด 15%', 'ตัดผมช่วงเวลา 10:00-14:00 น. รับส่วนลด 15%', 15.00, '2024-01-01', '2024-12-31');

-- ข้อมูลตัวอย่างแกลเลอรี่
INSERT INTO gallery (title, description, image, category, is_active) VALUES
('ทรงผมคลาสสิก', 'ทรงผมแบบคลาสสิกสำหรับผู้ชาย เหมาะกับทุกโอกาส', 'classic_haircut.jpg', 'haircut', 1),
('ทรงผมโมเดิร์น', 'ทรงผมสไตล์โมเดิร์นที่ได้รับความนิยม', 'modern_style.jpg', 'haircut', 1),
('การโกนหนวด', 'บริการโกนหนวดแบบมืออาชีพด้วยมีดโกนแบบดั้งเดิม', 'beard_shaving.jpg', 'beard', 1),
('จัดแต่งทรงผม', 'การจัดแต่งทรงผมสำหรับงานพิเศษและงานสำคัญ', 'hair_styling.jpg', 'styling', 1),
('ผลงานก่อน-หลัง', 'ภาพเปรียบเทียบก่อนและหลังการตัดผม', 'before_after.jpg', 'before_after', 1),
('ทรงผมเด็ก', 'ทรงผมน่ารักสำหรับเด็กๆ', 'kids_haircut.jpg', 'haircut', 1),
('ดูแลเส้นผม', 'บริการดูแลและบำรุงเส้นผม', 'hair_treatment.jpg', 'treatment', 1);

-- =====================================================
-- SQL สำหรับอัปเดตตาราง services ที่มีอยู่แล้ว
-- (ใช้เมื่อต้องการเพิ่มคอลัมน์สำหรับ manage_services.php)
-- =====================================================

-- เพิ่มคอลัมน์ image หากยังไม่มี
ALTER TABLE services ADD COLUMN IF NOT EXISTS image VARCHAR(255) COMMENT 'ชื่อไฟล์รูปภาพบริการ (สำหรับ manage_services.php)' AFTER duration;

-- เพิ่มคอลัมน์ is_active หากยังไม่มี
ALTER TABLE services ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการเปิด/ปิดบริการ' AFTER image;

-- เพิ่มคอลัมน์ updated_at หากยังไม่มี
ALTER TABLE services ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไขล่าสุด' AFTER created_at;

-- อัปเดตข้อมูลบริการที่มีอยู่ให้มีสถานะเปิดใช้งาน
UPDATE services SET is_active = TRUE WHERE is_active IS NULL;

-- แสดงข้อความสำเร็จ
SELECT '🎉 ฐานข้อมูลพร้อมสำหรับ manage_services.php! 🎉' as 'SUCCESS';
