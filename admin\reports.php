<?php
/**
 * หน้ารายงานและสถิติสำหรับแอดมิน
 */

require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-t');

// สถิติการจองรายวัน
$daily_bookings = fetchAll("
    SELECT 
        DATE(booking_date) as date,
        COUNT(*) as total_bookings,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as revenue
    FROM bookings 
    WHERE booking_date BETWEEN ? AND ?
    GROUP BY DATE(booking_date)
    ORDER BY date DESC
", [$date_from, $date_to]);

// สถิติรายเดือน
$monthly_stats = fetchOne("
    SELECT 
        COUNT(*) as total_bookings,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as total_revenue,
        AVG(CASE WHEN status = 'completed' THEN total_price ELSE NULL END) as avg_revenue
    FROM bookings 
    WHERE booking_date BETWEEN ? AND ?
", [$date_from, $date_to]);

// สถิติบริการยอดนิยม
$popular_services = fetchAll("
    SELECT 
        s.name,
        COUNT(b.id) as booking_count,
        SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END) as revenue
    FROM services s
    LEFT JOIN bookings b ON s.id = b.service_id 
        AND b.booking_date BETWEEN ? AND ?
    GROUP BY s.id, s.name
    ORDER BY booking_count DESC
    LIMIT 10
", [$date_from, $date_to]);

// สถิติช่างตัดผม
$barber_stats = fetchAll("
    SELECT 
        br.name,
        COUNT(b.id) as booking_count,
        SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
        SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END) as revenue
    FROM barbers br
    LEFT JOIN bookings b ON br.id = b.barber_id 
        AND b.booking_date BETWEEN ? AND ?
    WHERE br.is_active = 1
    GROUP BY br.id, br.name
    ORDER BY booking_count DESC
", [$date_from, $date_to]);

// สถิติลูกค้า
$customer_stats = fetchAll("
    SELECT 
        customer_name,
        customer_phone,
        COUNT(*) as booking_count,
        SUM(CASE WHEN status = 'completed' THEN total_price ELSE 0 END) as total_spent,
        MAX(booking_date) as last_booking
    FROM bookings 
    WHERE booking_date BETWEEN ? AND ?
    GROUP BY customer_name, customer_phone
    HAVING booking_count > 1
    ORDER BY booking_count DESC, total_spent DESC
    LIMIT 20
", [$date_from, $date_to]);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รายงานและสถิติ - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-cut"></i> Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-arrow-left"></i> กลับแดชบอร์ด
                </a>
                <a class="nav-link" href="../public/logout.php">
                    <i class="fas fa-sign-out-alt"></i> ออกจากระบบ
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-chart-bar text-primary"></i> รายงานและสถิติ</h2>
                <p class="text-muted">ข้อมูลสถิติและรายงานการใช้งานระบบ</p>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> เลือกช่วงวันที่</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">วันที่เริ่มต้น</label>
                        <input type="date" class="form-control" name="date_from" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">วันที่สิ้นสุด</label>
                        <input type="date" class="form-control" name="date_to" value="<?= $date_to ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search"></i> ดูรายงาน
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <h3><?= number_format($monthly_stats['total_bookings']) ?></h3>
                        <p>การจองทั้งหมด</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?= number_format($monthly_stats['completed']) ?></h3>
                        <p>เสร็จสิ้นแล้ว</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?= number_format($monthly_stats['pending'] + $monthly_stats['confirmed']) ?></h3>
                        <p>รอดำเนินการ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <h3><?= number_format($monthly_stats['total_revenue'], 2) ?></h3>
                        <p>รายได้ (บาท)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-line"></i> การจองรายวัน</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="dailyChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-pie"></i> สถานะการจอง</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Services -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-star"></i> บริการยอดนิยม</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>บริการ</th>
                                        <th>จำนวนจอง</th>
                                        <th>รายได้</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($popular_services as $service): ?>
                                        <tr>
                                            <td><?= sanitize($service['name']) ?></td>
                                            <td><span class="badge bg-primary"><?= $service['booking_count'] ?></span></td>
                                            <td><?= number_format($service['revenue'], 2) ?> บาท</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-user-tie"></i> สถิติช่างตัดผม</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ช่าง</th>
                                        <th>จำนวนจอง</th>
                                        <th>เสร็จสิ้น</th>
                                        <th>รายได้</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($barber_stats as $barber): ?>
                                        <tr>
                                            <td><?= sanitize($barber['name']) ?></td>
                                            <td><span class="badge bg-info"><?= $barber['booking_count'] ?></span></td>
                                            <td><span class="badge bg-success"><?= $barber['completed_count'] ?></span></td>
                                            <td><?= number_format($barber['revenue'], 2) ?> บาท</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h6><i class="fas fa-users"></i> ลูกค้าประจำ (จองมากกว่า 1 ครั้ง)</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ชื่อลูกค้า</th>
                                <th>เบอร์โทร</th>
                                <th>จำนวนจอง</th>
                                <th>ยอดใช้จ่าย</th>
                                <th>การจองล่าสุด</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customer_stats as $customer): ?>
                                <tr>
                                    <td><?= sanitize($customer['customer_name']) ?></td>
                                    <td><?= sanitize($customer['customer_phone']) ?></td>
                                    <td><span class="badge bg-primary"><?= $customer['booking_count'] ?></span></td>
                                    <td><?= number_format($customer['total_spent'], 2) ?> บาท</td>
                                    <td><?= formatDateThai($customer['last_booking']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Daily Details -->
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-calendar-alt"></i> รายละเอียดรายวัน</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>วันที่</th>
                                <th>การจองทั้งหมด</th>
                                <th>เสร็จสิ้น</th>
                                <th>ยกเลิก</th>
                                <th>รายได้</th>
                                <th>อัตราสำเร็จ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($daily_bookings as $day): ?>
                                <?php 
                                $success_rate = $day['total_bookings'] > 0 ? 
                                    ($day['completed'] / $day['total_bookings']) * 100 : 0;
                                ?>
                                <tr>
                                    <td><?= formatDateThai($day['date']) ?></td>
                                    <td><span class="badge bg-primary"><?= $day['total_bookings'] ?></span></td>
                                    <td><span class="badge bg-success"><?= $day['completed'] ?></span></td>
                                    <td><span class="badge bg-danger"><?= $day['cancelled'] ?></span></td>
                                    <td><?= number_format($day['revenue'], 2) ?> บาท</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: <?= $success_rate ?>%">
                                                <?= number_format($success_rate, 1) ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Daily Bookings Chart
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        const dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [<?php echo implode(',', array_map(function($day) { 
                    return '"' . formatDateThai($day['date']) . '"'; 
                }, array_reverse($daily_bookings))); ?>],
                datasets: [{
                    label: 'การจองรายวัน',
                    data: [<?php echo implode(',', array_map(function($day) { 
                        return $day['total_bookings']; 
                    }, array_reverse($daily_bookings))); ?>],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['เสร็จสิ้น', 'รอยืนยัน', 'ยืนยันแล้ว', 'ยกเลิก'],
                datasets: [{
                    data: [
                        <?= $monthly_stats['completed'] ?>,
                        <?= $monthly_stats['pending'] ?>,
                        <?= $monthly_stats['confirmed'] ?>,
                        <?= $monthly_stats['cancelled'] ?>
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#17a2b8',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html>
