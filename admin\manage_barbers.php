<?php
/**
 * หน้าจัดการช่างตัดผมสำหรับแอดมิน
 */

require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'add_barber') {
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $speciality = trim($_POST['speciality'] ?? '');

        $csrf_token = $_POST['csrf_token'] ?? '';
        
        // ตรวจสอบ CSRF Token
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
        }
        // ตรวจสอบข้อมูลที่จำเป็น
        elseif (empty($name)) {
            $error_message = 'กรุณากรอกชื่อช่างตัดผม';
        }
        elseif (empty($phone)) {
            $error_message = 'กรุณากรอกเบอร์โทรศัพท์';
        }
        else {
            $image_filename = null;
            
            // ตรวจสอบการอัพโหลดรูปภาพ (ไม่บังคับ)
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $max_size = 5 * 1024 * 1024; // 5MB
                
                if (!in_array($file['type'], $allowed_types)) {
                    $error_message = 'รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น';
                } elseif ($file['size'] > $max_size) {
                    $error_message = 'ขนาดไฟล์ต้องไม่เกิน 5MB';
                } else {
                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $image_filename = 'barber_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
                    $upload_path = __DIR__ . '/../assets/img/barbers/' . $image_filename;
                    
                    // สร้างโฟลเดอร์ถ้ายังไม่มี
                    $barbers_dir = __DIR__ . '/../assets/img/barbers/';
                    if (!is_dir($barbers_dir)) {
                        mkdir($barbers_dir, 0755, true);
                    }
                    
                    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
                        $error_message = 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์';
                        $image_filename = null;
                    }
                }
            }
            
            if (empty($error_message)) {
                $sql = "INSERT INTO barbers (name, phone, email, speciality, image, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, 1, NOW())";
                $result = executeQuery($sql, [$name, $phone, $email, $speciality, $image_filename]);
                
                if ($result) {
                    $success_message = 'เพิ่มช่างตัดผมเรียบร้อยแล้ว';
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล';
                    // ลบไฟล์ที่อัพโหลดแล้ว
                    if ($image_filename && file_exists($upload_path)) {
                        unlink($upload_path);
                    }
                }
            }
        }
    }
    elseif ($action === 'edit_barber') {
        $barber_id = intval($_POST['barber_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $speciality = trim($_POST['speciality'] ?? '');

        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง';
        } elseif ($barber_id <= 0) {
            $error_message = 'ไม่พบช่างตัดผมที่ต้องการแก้ไข';
        } elseif (empty($name)) {
            $error_message = 'กรุณากรอกชื่อช่างตัดผม';
        } elseif (empty($phone)) {
            $error_message = 'กรุณากรอกเบอร์โทรศัพท์';
        } else {
            // ดึงข้อมูลช่างตัดผมเดิม
            $barber = fetchOne("SELECT * FROM barbers WHERE id = ?", [$barber_id]);
            
            if ($barber) {
                $image_filename = $barber['image']; // ใช้รูปเดิม
                
                // ตรวจสอบการอัพโหลดรูปภาพใหม่
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['image'];
                    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    $max_size = 5 * 1024 * 1024; // 5MB
                    
                    if (!in_array($file['type'], $allowed_types)) {
                        $error_message = 'รองรับเฉพาะไฟล์ JPG, PNG, GIF เท่านั้น';
                    } elseif ($file['size'] > $max_size) {
                        $error_message = 'ขนาดไฟล์ต้องไม่เกิน 5MB';
                    } else {
                        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        $new_image_filename = 'barber_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
                        $upload_path = __DIR__ . '/../assets/img/barbers/' . $new_image_filename;
                        
                        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                            // ลบรูปเดิม
                            if ($image_filename && file_exists(__DIR__ . '/../assets/img/barbers/' . $image_filename)) {
                                unlink(__DIR__ . '/../assets/img/barbers/' . $image_filename);
                            }
                            $image_filename = $new_image_filename;
                        } else {
                            $error_message = 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์';
                        }
                    }
                }
                
                if (empty($error_message)) {
                    $sql = "UPDATE barbers SET name = ?, phone = ?, email = ?, speciality = ?, image = ?, updated_at = NOW() WHERE id = ?";
                    $result = executeQuery($sql, [$name, $phone, $email, $speciality, $image_filename, $barber_id]);
                    
                    if ($result) {
                        $success_message = 'แก้ไขข้อมูลช่างตัดผมเรียบร้อยแล้ว';
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล';
                    }
                }
            } else {
                $error_message = 'ไม่พบช่างตัดผมที่ต้องการแก้ไข';
            }
        }
    }
    elseif ($action === 'delete_barber') {
        $barber_id = intval($_POST['barber_id'] ?? 0);
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง';
        } elseif ($barber_id <= 0) {
            $error_message = 'ไม่พบช่างตัดผมที่ต้องการลบ';
        } else {
            // ตรวจสอบว่ามีการจองที่ใช้ช่างตัดผมนี้หรือไม่
            $booking_count = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE barber_id = ?", [$barber_id])['count'];
            
            if ($booking_count > 0) {
                $error_message = 'ไม่สามารถลบช่างตัดผมนี้ได้ เนื่องจากมีการจองที่ใช้ช่างตัดผมนี้อยู่';
            } else {
                // ดึงข้อมูลช่างตัดผม
                $barber = fetchOne("SELECT * FROM barbers WHERE id = ?", [$barber_id]);
                
                if ($barber) {
                    // ลบไฟล์รูปภาพ
                    if ($barber['image'] && file_exists(__DIR__ . '/../assets/img/barbers/' . $barber['image'])) {
                        unlink(__DIR__ . '/../assets/img/barbers/' . $barber['image']);
                    }
                    
                    // ลบข้อมูลจากฐานข้อมูล
                    $result = executeQuery("DELETE FROM barbers WHERE id = ?", [$barber_id]);
                    
                    if ($result) {
                        $success_message = 'ลบช่างตัดผมเรียบร้อยแล้ว';
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการลบข้อมูล';
                    }
                } else {
                    $error_message = 'ไม่พบช่างตัดผมที่ต้องการลบ';
                }
            }
        }
    }
    elseif ($action === 'toggle_status') {
        $barber_id = intval($_POST['barber_id'] ?? 0);
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!verifyCSRFToken($csrf_token)) {
            $error_message = 'การร้องขอไม่ถูกต้อง';
        } elseif ($barber_id <= 0) {
            $error_message = 'ไม่พบช่างตัดผม';
        } else {
            $result = executeQuery("UPDATE barbers SET is_active = NOT is_active WHERE id = ?", [$barber_id]);
            
            if ($result) {
                $success_message = 'อัพเดทสถานะเรียบร้อยแล้ว';
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการอัพเดทสถานะ';
            }
        }
    }
}

// ดึงข้อมูลช่างตัดผมทั้งหมด
$barbers = fetchAll("SELECT * FROM barbers ORDER BY created_at DESC");

// นับจำนวนช่างตัดผม
$total_barbers = count($barbers);
$active_barbers = count(array_filter($barbers, function($barber) { return $barber['is_active']; }));

// ดึงข้อมูลสำหรับแก้ไข
$edit_barber = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_barber = fetchOne("SELECT * FROM barbers WHERE id = ?", [intval($_GET['edit'])]);
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการช่างตัดผม - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .barber-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }
        
        .barber-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .barber-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        }
        

        
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .btn-action {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 0 2px;
        }
        
        .speciality-list {
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_bookings.php">จัดการการจอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php">จัดการผู้ใช้</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_services.php">จัดการบริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_barbers.php">จัดการช่างตัดผม</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_gallery.php">จัดการแกลเลอรี่</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-user-tie text-primary"></i> จัดการช่างตัดผม
                </h2>
                <p class="text-muted">เพิ่ม แก้ไข และจัดการข้อมูลช่างตัดผม</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBarberModal">
                    <i class="fas fa-plus"></i> เพิ่มช่างตัดผม
                </button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>ช่างตัดผมทั้งหมด</h5>
                                <h2><?= $total_barbers ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-tie fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>ช่างที่ปฏิบัติงาน</h5>
                                <h2><?= $active_barbers ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5>ช่างที่หยุดงาน</h5>
                                <h2><?= $total_barbers - $active_barbers ?></h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Barbers Grid -->
        <div class="row">
            <?php if (empty($barbers)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-user-tie fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">ยังไม่มีช่างตัดผม</h4>
                        <p class="text-muted">คลิกปุ่ม "เพิ่มช่างตัดผม" เพื่อเริ่มต้น</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBarberModal">
                            <i class="fas fa-plus"></i> เพิ่มช่างตัดผมคนแรก
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($barbers as $barber): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card barber-card h-100">
                            <div class="position-relative">
                                <span class="status-badge">
                                    <?php if ($barber['is_active']): ?>
                                        <span class="badge bg-success">ปฏิบัติงาน</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">หยุดงาน</span>
                                    <?php endif; ?>
                                </span>

                                <?php if ($barber['image']): ?>
                                    <img src="../assets/img/barbers/<?= sanitize($barber['image']) ?>"
                                         alt="<?= sanitize($barber['name']) ?>"
                                         class="barber-image"
                                         onerror="this.src='../assets/img/placeholder.jpg'">
                                <?php else: ?>
                                    <div class="barber-image d-flex align-items-center justify-content-center">
                                        <i class="fas fa-user-tie fa-4x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?= sanitize($barber['name']) ?></h5>

                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-phone"></i> <?= sanitize($barber['phone']) ?>
                                    </small>
                                </div>

                                <?php if ($barber['email']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-envelope"></i> <?= sanitize($barber['email']) ?>
                                        </small>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($barber['speciality']) && !empty($barber['speciality'])): ?>
                                    <div class="mb-3 flex-grow-1">
                                        <div class="speciality-list">
                                            <strong>ความเชี่ยวชาญ:</strong><br>
                                            <?= sanitize($barber['speciality']) ?>
                                        </div>
                                    </div>
                                <?php endif; ?>



                                <div class="d-flex justify-content-center">
                                    <button class="btn btn-outline-primary btn-action"
                                            onclick="editBarber(<?= $barber['id'] ?>)"
                                            title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-<?= $barber['is_active'] ? 'warning' : 'success' ?> btn-action"
                                            onclick="toggleBarberStatus(<?= $barber['id'] ?>)"
                                            title="<?= $barber['is_active'] ? 'หยุดงาน' : 'เริ่มงาน' ?>">
                                        <i class="fas fa-<?= $barber['is_active'] ? 'pause' : 'play' ?>"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-action"
                                            onclick="deleteBarber(<?= $barber['id'] ?>, '<?= sanitize($barber['name']) ?>')"
                                            title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="card-footer bg-light">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i>
                                    เข้าร่วมเมื่อ: <?= date('d/m/Y', strtotime($barber['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Barber Modal -->
    <div class="modal fade" id="addBarberModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus"></i> เพิ่มช่างตัดผมใหม่
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_barber">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="barberName" name="name" required>
                                    <label for="barberName">ชื่อ-นามสกุล *</label>
                                    <div class="invalid-feedback">กรุณากรอกชื่อ-นามสกุล</div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="barberPhone" name="phone" required>
                                    <label for="barberPhone">เบอร์โทรศัพท์ *</label>
                                    <div class="invalid-feedback">กรุณากรอกเบอร์โทรศัพท์</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="barberEmail" name="email">
                                    <label for="barberEmail">อีเมล</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="barberSpeciality" class="form-label">ความเชี่ยวชาญ</label>
                            <textarea class="form-control" id="barberSpeciality" name="speciality" rows="3"
                                      placeholder="เช่น ตัดผมทรงคลาสสิก, โกนหนวด, จัดแต่งทรงผม"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="barberImage" class="form-label">รูปภาพช่างตัดผม</label>
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-0">คลิกเพื่อเลือกรูปภาพ</p>
                                <small class="text-muted">JPG, PNG, GIF ไม่เกิน 5MB</small>
                                <input type="file" class="form-control" id="barberImage" name="image"
                                       accept="image/*" style="display: none;">
                            </div>
                            <div id="imagePreview" class="mt-2" style="display: none;">
                                <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 150px;">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึก
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Barber Modal -->
    <div class="modal fade" id="editBarberModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> แก้ไขข้อมูลช่างตัดผม
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit_barber">
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        <input type="hidden" name="barber_id" id="editBarberId">

                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="editBarberName" name="name" required>
                            <label for="editBarberName">ชื่อ-นามสกุล *</label>
                            <div class="invalid-feedback">กรุณากรอกชื่อ-นามสกุล</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="editBarberPhone" name="phone" required>
                                    <label for="editBarberPhone">เบอร์โทรศัพท์ *</label>
                                    <div class="invalid-feedback">กรุณากรอกเบอร์โทรศัพท์</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="editBarberEmail" name="email">
                                    <label for="editBarberEmail">อีเมล</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="editBarberSpeciality" class="form-label">ความเชี่ยวชาญ</label>
                            <textarea class="form-control" id="editBarberSpeciality" name="speciality" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="editBarberImage" class="form-label">รูปภาพช่างตัดผม</label>
                            <div class="upload-area" id="editUploadArea">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-0">คลิกเพื่อเปลี่ยนรูปภาพ</p>
                                <small class="text-muted">JPG, PNG, GIF ไม่เกิน 5MB</small>
                                <input type="file" class="form-control" id="editBarberImage" name="image"
                                       accept="image/*" style="display: none;">
                            </div>
                            <div id="editImagePreview" class="mt-2">
                                <img id="editPreviewImg" src="" alt="Current Image" class="img-fluid rounded" style="max-height: 150px;">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึกการแก้ไข
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden Forms for Actions -->
    <form id="toggleStatusForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        <input type="hidden" name="barber_id" id="toggleBarberId">
    </form>

    <form id="deleteBarberForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_barber">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        <input type="hidden" name="barber_id" id="deleteBarberId">
    </form>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Barbers data for JavaScript
        const barbersData = <?= json_encode($barbers) ?>;

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Upload area functionality
        function setupUploadArea(uploadAreaId, fileInputId, previewId, previewImgId) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);
            const preview = document.getElementById(previewId);
            const previewImg = document.getElementById(previewImgId);

            uploadArea.addEventListener('click', () => fileInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect(files[0], preview, previewImg);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0], preview, previewImg);
                }
            });
        }

        function handleFileSelect(file, preview, previewImg) {
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        }

        // Setup upload areas
        setupUploadArea('uploadArea', 'barberImage', 'imagePreview', 'previewImg');
        setupUploadArea('editUploadArea', 'editBarberImage', 'editImagePreview', 'editPreviewImg');

        // Edit barber function
        function editBarber(barberId) {
            const barber = barbersData.find(b => b.id == barberId);
            if (!barber) return;

            // Fill form data
            document.getElementById('editBarberId').value = barber.id;
            document.getElementById('editBarberName').value = barber.name;
            document.getElementById('editBarberPhone').value = barber.phone;
            document.getElementById('editBarberEmail').value = barber.email || '';
            document.getElementById('editBarberSpeciality').value = barber.speciality || '';

            // Show current image
            const editPreviewImg = document.getElementById('editPreviewImg');
            const editImagePreview = document.getElementById('editImagePreview');

            if (barber.image) {
                editPreviewImg.src = `../assets/img/barbers/${barber.image}`;
                editImagePreview.style.display = 'block';
            } else {
                editImagePreview.style.display = 'none';
            }

            // Show modal
            new bootstrap.Modal(document.getElementById('editBarberModal')).show();
        }

        // Toggle barber status
        function toggleBarberStatus(barberId) {
            const barber = barbersData.find(b => b.id == barberId);
            if (!barber) return;

            const action = barber.is_active ? 'หยุดงาน' : 'เริ่มงาน';

            Swal.fire({
                title: `ยืนยันการ${action}`,
                text: `คุณต้องการ${action}ของ ${barber.name} หรือไม่?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `ใช่, ${action}`,
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('toggleBarberId').value = barberId;
                    document.getElementById('toggleStatusForm').submit();
                }
            });
        }

        // Delete barber
        function deleteBarber(barberId, barberName) {
            Swal.fire({
                title: 'ยืนยันการลบ',
                text: `คุณต้องการลบช่างตัดผม "${barberName}" หรือไม่?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ใช่, ลบเลย',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById('deleteBarberId').value = barberId;
                    document.getElementById('deleteBarberForm').submit();
                }
            });
        }

        // Auto-hide alerts
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    alert.classList.add('fade');
                    setTimeout(() => alert.remove(), 150);
                }
            });
        }, 5000);
    </script>
</body>
</html>
