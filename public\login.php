<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// ป้องกันการเข้าถึงหากเข้าสู่ระบบแล้ว
redirectIfLoggedIn();

$error_message = '';
$success_message = '';

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($username) || empty($password)) {
        $error_message = 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน';
    }
    // ตรวจสอบการพยายามเข้าสู่ระบบมากเกินไป
    elseif (!checkLoginAttempts($_SERVER['REMOTE_ADDR'])) {
        $error_message = 'คุณพยายามเข้าสู่ระบบมากเกินไป กรุณารอ 15 นาทีแล้วลองใหม่';
    }
    else {
        // พยายามเข้าสู่ระบบ
        if (login($username, $password)) {
            // เข้าสู่ระบบสำเร็จ
            if (isAdmin()) {
                header('Location: ../admin/dashboard.php');
            } else {
                header('Location: ../customer/dashboard.php');
            }
            exit;
        } else {
            // เข้าสู่ระบบไม่สำเร็จ
            logFailedLogin($username, $_SERVER['REMOTE_ADDR']);
            $error_message = 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง';
        }
    }
}

// ตรวจสอบข้อความจาก URL parameters
if (isset($_GET['message'])) {
    switch ($_GET['message']) {
        case 'registered':
            $success_message = 'สมัครสมาชิกสำเร็จ กรุณาเข้าสู่ระบบ';
            break;
        case 'logout':
            $success_message = 'ออกจากระบบเรียบร้อยแล้ว';
            break;
        case 'login_required':
            $error_message = 'กรุณาเข้าสู่ระบบก่อนใช้งาน';
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card">
                        <div class="auth-header">
                            <h2><i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ</h2>
                            <p class="mb-0">ร้านตัดผม Barber Shop</p>
                        </div>
                        
                        <div class="auth-body">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user"></i> ชื่อผู้ใช้หรืออีเมล
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           name="username" 
                                           value="<?= sanitize($_POST['username'] ?? '') ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        กรุณากรอกชื่อผู้ใช้หรืออีเมล
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock"></i> รหัสผ่าน
                                    </label>
                                    <div class="input-group">
                                        <input type="password" 
                                               class="form-control" 
                                               id="password" 
                                               name="password" 
                                               required>
                                        <button class="btn btn-outline-secondary" 
                                                type="button" 
                                                onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        กรุณากรอกรหัสผ่าน
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        จดจำการเข้าสู่ระบบ
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">ยังไม่มีบัญชี?</p>
                                <a href="register.php" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus"></i> สมัครสมาชิก
                                </a>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="index.php" class="text-muted">
                                    <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ข้อมูลสำหรับทดสอบ -->
                    <div class="card mt-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle text-info"></i> ข้อมูลสำหรับทดสอบ
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>ผู้ดูแลระบบ:</strong><br>
                                    ชื่อผู้ใช้: admin<br>
                                    รหัสผ่าน: password
                                </div>
                                <div class="col-md-6">
                                    <strong>ลูกค้า:</strong><br>
                                    สมัครสมาชิกใหม่<br>
                                    หรือใช้บัญชีที่มีอยู่
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        // ฟังก์ชันแสดง/ซ่อนรหัสผ่าน
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const eye = document.getElementById(inputId + '-eye');
            
            if (input.type === 'password') {
                input.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }
        
        // Focus ที่ input แรก
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
