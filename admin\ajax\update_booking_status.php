<?php
/**
 * ไฟล์ AJAX สำหรับอัพเดทสถานะการจอง
 */

require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/auth.php';

header('Content-Type: application/json');

// ตรวจสอบการเข้าสู่ระบบและสิทธิ์แอดมิน
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง']);
    exit;
}

$booking_id = $_POST['booking_id'] ?? '';
$new_status = $_POST['status'] ?? '';

// ตรวจสอบข้อมูลที่จำเป็น
if (empty($booking_id) || empty($new_status)) {
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน']);
    exit;
}

// ตรวจสอบสถานะที่ถูกต้อง
$valid_statuses = ['pending', 'confirmed', 'completed', 'cancelled'];
if (!in_array($new_status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'สถานะไม่ถูกต้อง']);
    exit;
}

// ตรวจสอบว่าการจองมีอยู่จริง
$booking = fetchOne("SELECT * FROM bookings WHERE id = ?", [$booking_id]);
if (!$booking) {
    echo json_encode(['success' => false, 'message' => 'ไม่พบการจองที่ระบุ']);
    exit;
}

// อัพเดทสถานะ
$sql = "UPDATE bookings SET status = ?, updated_at = NOW() WHERE id = ?";
$result = executeQuery($sql, [$new_status, $booking_id]);

if ($result) {
    // ส่งอีเมลแจ้งเตือนลูกค้า
    sendBookingStatusUpdate($booking_id, $new_status);
    
    // ส่งแจ้งเตือน LINE Notify
    if (function_exists('sendBookingStatusNotification')) {
        sendBookingStatusNotification($booking_id, $new_status);
    }
    
    echo json_encode(['success' => true, 'message' => 'อัพเดทสถานะเรียบร้อยแล้ว']);
} else {
    echo json_encode(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการอัพเดทสถานะ']);
}
?>
