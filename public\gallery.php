<?php
require_once __DIR__ . '/../includes/functions.php';

// ดึงข้อมูลแกลเลอรี่
$gallery_items = fetchAll("SELECT * FROM gallery WHERE is_active = 1 ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แกลเลอรี่ - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="promotion.php">โปรโมชั่น</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="gallery.php">แกลเลอรี่</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">เกี่ยวกับเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (isAdmin()): ?>
                                    <li><a class="dropdown-item" href="../admin/dashboard.php">แดชบอร์ดแอดมิน</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="../customer/dashboard.php">แดชบอร์ด</a></li>
                                    <li><a class="dropdown-item" href="../customer/book.php">จองคิว</a></li>
                                    <li><a class="dropdown-item" href="../customer/my_bookings.php">การจองของฉัน</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">ออกจากระบบ</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">เข้าสู่ระบบ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">สมัครสมาชิก</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 bg-success text-white" style="margin-top: 76px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-images"></i> แกลเลอรี่
                    </h1>
                    <p class="lead">ชมผลงานและบรรยากาศภายในร้าน</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="py-5">
        <div class="container">
            <?php if (empty($gallery_items)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-camera fa-3x mb-3"></i>
                            <h4>กำลังอัพเดทรูปภาพ</h4>
                            <p>แกลเลอรี่จะอัพเดทในเร็วๆ นี้ กรุณาติดตามต่อไป</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row g-4" id="gallery-container">
                    <?php foreach ($gallery_items as $item): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="gallery-item">
                                <img src="../assets/img/gallery/<?= sanitize($item['image']) ?>"
                                     alt="<?= sanitize($item['title']) ?>"
                                     class="img-fluid"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     data-image="../assets/img/gallery/<?= sanitize($item['image']) ?>"
                                     onerror="this.src='../assets/img/placeholder.jpg'"
                                     data-title="<?= sanitize($item['title']) ?>"
                                     data-description="<?= sanitize($item['description']) ?>">
                                
                                <div class="gallery-overlay">
                                    <div class="text-center">
                                        <h5><?= sanitize($item['title']) ?></h5>
                                        <?php if ($item['description']): ?>
                                            <p><?= sanitize($item['description']) ?></p>
                                        <?php endif; ?>
                                        <i class="fas fa-search-plus fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Sample Gallery (ถ้าไม่มีข้อมูลในฐานข้อมูล) -->
    <?php if (empty($gallery_items)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold">ตัวอย่างผลงาน</h2>
                    <p class="lead text-muted">ผลงานและบรรยากาศของร้าน</p>
                </div>
            </div>
            
            <div class="row g-4">
                <!-- Sample images -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-cut fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">ทรงผมคลาสสิก</h5>
                            <p class="card-text">ทรงผมแบบดั้งเดิมที่เหมาะกับทุกวัย</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-scissors fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">ทรงผมสมัยใหม่</h5>
                            <p class="card-text">ทรงผมแบบทันสมัยตามเทรนด์</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-store fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">บรรยากาศร้าน</h5>
                            <p class="card-text">บรรยากาศภายในร้านที่อบอุ่น</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-chair fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">เก้าอี้ตัดผม</h5>
                            <p class="card-text">เก้าอี้ตัดผมที่สะดวกสบาย</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-tools fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">เครื่องมือ</h5>
                            <p class="card-text">เครื่องมือคุณภาพสูงและทันสมัย</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                            <i class="fas fa-users fa-4x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">ทีมงาน</h5>
                            <p class="card-text">ทีมช่างมืออาชีพที่พร้อมให้บริการ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Call to Action -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4">ประทับใจกับผลงานของเรา?</h3>
                    <p class="lead mb-4">
                        มาสัมผัสประสบการณ์การตัดผมที่ดีที่สุดกับเรา
                    </p>
                    
                    <?php if (isLoggedIn() && !isAdmin()): ?>
                        <a href="../customer/book.php" class="btn btn-warning btn-lg me-3">
                            <i class="fas fa-calendar-plus"></i> จองคิวเลย
                        </a>
                        <a href="services.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list"></i> ดูบริการ
                        </a>
                    <?php elseif (!isLoggedIn()): ?>
                        <a href="register.php" class="btn btn-warning btn-lg me-3">
                            <i class="fas fa-user-plus"></i> สมัครสมาชิก
                        </a>
                        <a href="services.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list"></i> ดูบริการ
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalTitle">รูปภาพ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="img-fluid">
                    <p id="modalDescription" class="mt-3 text-muted"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-cut"></i> Barber Shop</h5>
                    <p>ร้านตัดผมมืออาชีพ ให้บริการด้วยใจ มีคุณภาพ</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                </div>
                <div class="col-md-4">
                    <h5>เวลาทำการ</h5>
                    <p>จันทร์ - เสาร์: 09:00 - 20:00</p>
                    <p>อาทิตย์: 10:00 - 18:00</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Barber Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        // Gallery modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const galleryImages = document.querySelectorAll('.gallery-item img[data-bs-toggle="modal"]');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('imageModalTitle');
            const modalDescription = document.getElementById('modalDescription');
            
            galleryImages.forEach(function(img) {
                img.addEventListener('click', function() {
                    const imageSrc = this.getAttribute('data-image');
                    const imageTitle = this.getAttribute('data-title');
                    const imageDescription = this.getAttribute('data-description');
                    
                    modalImage.src = imageSrc;
                    modalImage.alt = imageTitle;
                    modalTitle.textContent = imageTitle;
                    modalDescription.textContent = imageDescription || '';
                });
            });
        });
    </script>

    <style>
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }
        
        .gallery-item:hover img {
            transform: scale(1.1);
        }
    </style>
</body>
</html>
