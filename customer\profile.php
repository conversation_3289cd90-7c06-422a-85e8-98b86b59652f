<?php
require_once __DIR__ . '/../includes/auth.php';
requireCustomer();

$success_message = '';
$error_message = '';

$user_id = $_SESSION['user_id'];

// ดึงข้อมูลผู้ใช้
$user = fetchOne("SELECT * FROM users WHERE id = ?", [$user_id]);

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // ตรวจสอบ CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'การร้องขอไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
    }
    // ตรวจสอบข้อมูลที่จำเป็น
    elseif (empty($full_name) || empty($email)) {
        $error_message = 'กรุณากรอกชื่อและอีเมลให้ครบถ้วน';
    }
    // ตรวจสอบรูปแบบอีเมล
    elseif (!isValidEmail($email)) {
        $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
    }
    // ตรวจสอบเบอร์โทรศัพท์
    elseif (!empty($phone) && !isValidPhone($phone)) {
        $error_message = 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง';
    }
    else {
        // ตรวจสอบว่าอีเมลซ้ำกับผู้อื่นหรือไม่
        $existing_user = fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
        
        if ($existing_user) {
            $error_message = 'อีเมลนี้มีผู้ใช้งานแล้ว';
        } else {
            $update_fields = ['full_name' => $full_name, 'email' => $email, 'phone' => $phone];
            
            // ตรวจสอบการเปลี่ยนรหัสผ่าน
            if (!empty($new_password)) {
                if (empty($current_password)) {
                    $error_message = 'กรุณากรอกรหัสผ่านปัจจุบัน';
                } elseif (!password_verify($current_password, $user['password'])) {
                    $error_message = 'รหัสผ่านปัจจุบันไม่ถูกต้อง';
                } elseif (strlen($new_password) < 6) {
                    $error_message = 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
                } elseif ($new_password !== $confirm_password) {
                    $error_message = 'รหัสผ่านใหม่และการยืนยันไม่ตรงกัน';
                } else {
                    $update_fields['password'] = password_hash($new_password, PASSWORD_DEFAULT);
                }
            }
            
            if (empty($error_message)) {
                // อัพเดทข้อมูล
                $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($update_fields)));
                $sql = "UPDATE users SET $set_clause WHERE id = ?";
                $params = array_merge(array_values($update_fields), [$user_id]);
                
                $result = executeQuery($sql, $params);
                
                if ($result) {
                    // อัพเดท session
                    $_SESSION['full_name'] = $full_name;
                    $success_message = 'อัพเดทข้อมูลเรียบร้อยแล้ว';
                    
                    // ดึงข้อมูลใหม่
                    $user = fetchOne("SELECT * FROM users WHERE id = ?", [$user_id]);
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล';
                }
            }
        }
    }
}

// ดึงสถิติการใช้งาน
$total_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ?", [$user_id])['count'];
$completed_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status = 'completed'", [$user_id])['count'];
$total_spent = fetchOne("SELECT COALESCE(SUM(total_price), 0) as total FROM bookings WHERE user_id = ? AND status = 'completed'", [$user_id])['total'];
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>โปรไฟล์ - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book.php">จองคิว</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my_bookings.php">การจองของฉัน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../public/services.php">บริการ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-user-edit text-primary"></i> โปรไฟล์ของฉัน
                </h2>
                <p class="text-muted">จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชี</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="dashboard.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> กลับแดชบอร์ด
                </a>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i> แก้ไขข้อมูลส่วนตัว
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                            
                            <!-- Basic Information -->
                            <h6 class="text-primary mb-3">ข้อมูลพื้นฐาน</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">ชื่อผู้ใช้</label>
                                        <input type="text" class="form-control" id="username" value="<?= sanitize($user['username']) ?>" readonly>
                                        <div class="form-text">ไม่สามารถเปลี่ยนชื่อผู้ใช้ได้</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">ชื่อ-นามสกุล *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?= sanitize($user['full_name']) ?>" required>
                                        <div class="invalid-feedback">กรุณากรอกชื่อ-นามสกุล</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">อีเมล *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?= sanitize($user['email']) ?>" required>
                                        <div class="invalid-feedback">กรุณากรอกอีเมลที่ถูกต้อง</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">เบอร์โทรศัพท์</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?= sanitize($user['phone']) ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <!-- Change Password -->
                            <h6 class="text-primary mb-3">เปลี่ยนรหัสผ่าน</h6>
                            <p class="text-muted">หากไม่ต้องการเปลี่ยนรหัสผ่าน ให้เว้นว่างไว้</p>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">รหัสผ่านปัจจุบัน</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password">
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">รหัสผ่านใหม่</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" minlength="6">
                                        <div class="form-text">อย่างน้อย 6 ตัวอักษร</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">ยืนยันรหัสผ่านใหม่</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                        <div class="invalid-feedback" id="password-feedback">รหัสผ่านไม่ตรงกัน</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> บันทึกการเปลี่ยนแปลง
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Profile Stats -->
            <div class="col-lg-4">
                <!-- Account Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> ข้อมูลบัญชี
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>สมาชิกเมื่อ:</strong><br>
                            <?= formatDateThai(date('Y-m-d', strtotime($user['created_at']))) ?>
                        </div>
                        
                        <div class="mb-3">
                            <strong>เข้าสู่ระบบล่าสุด:</strong><br>
                            <?= $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'ไม่มีข้อมูล' ?>
                        </div>
                        
                        <div class="mb-0">
                            <strong>สถานะบัญชี:</strong><br>
                            <span class="badge bg-success">ใช้งานได้</span>
                        </div>
                    </div>
                </div>

                <!-- Usage Stats -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i> สถิติการใช้งาน
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="stat-item mb-3">
                            <div class="d-flex justify-content-between">
                                <span>การจองทั้งหมด:</span>
                                <strong class="text-primary"><?= $total_bookings ?> ครั้ง</strong>
                            </div>
                        </div>
                        
                        <div class="stat-item mb-3">
                            <div class="d-flex justify-content-between">
                                <span>ใช้บริการแล้ว:</span>
                                <strong class="text-success"><?= $completed_bookings ?> ครั้ง</strong>
                            </div>
                        </div>
                        
                        <div class="stat-item mb-3">
                            <div class="d-flex justify-content-between">
                                <span>ยอดใช้จ่ายรวม:</span>
                                <strong class="text-warning"><?= number_format($total_spent) ?> บาท</strong>
                            </div>
                        </div>
                        
                        <?php if ($completed_bookings > 0): ?>
                        <div class="stat-item">
                            <div class="d-flex justify-content-between">
                                <span>ค่าใช้จ่ายเฉลี่ย:</span>
                                <strong class="text-info"><?= number_format($total_spent / $completed_bookings) ?> บาท/ครั้ง</strong>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        // ตรวจสอบรหัสผ่าน
        document.addEventListener('DOMContentLoaded', function() {
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            const feedback = document.getElementById('password-feedback');
            
            function validatePasswords() {
                if (newPassword.value && confirmPassword.value) {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('รหัสผ่านไม่ตรงกัน');
                        feedback.textContent = 'รหัสผ่านไม่ตรงกัน';
                        feedback.className = 'invalid-feedback';
                    } else {
                        confirmPassword.setCustomValidity('');
                        feedback.textContent = 'รหัสผ่านตรงกัน';
                        feedback.className = 'valid-feedback';
                    }
                }
            }
            
            newPassword.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);
        });
    </script>
</body>
</html>
