<?php
/**
 * ไฟล์ AJAX สำหรับดึงช่วงเวลาที่ว่าง
 */

require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/auth.php';

header('Content-Type: application/json');

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

$date = $_POST['date'] ?? '';
$barber_id = $_POST['barber_id'] ?? '';
$service_id = $_POST['service_id'] ?? '';

// ตรวจสอบข้อมูลที่จำเป็น
if (empty($date) || empty($barber_id) || empty($service_id)) {
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ครบถ้วน']);
    exit;
}

// ตรวจสอบวันที่ (อนุญาตให้จองได้ตั้งแต่วันนี้)
if ($date < date('Y-m-d')) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเลือกวันที่ในอนาคต']);
    exit;
}

// ดึงข้อมูลบริการ
$service = fetchOne("SELECT duration FROM services WHERE id = ? AND is_active = 1", [$service_id]);
if (!$service) {
    echo json_encode(['success' => false, 'message' => 'ไม่พบบริการที่เลือก']);
    exit;
}

// กำหนดช่วงเวลาทำการ
$work_start = '09:00';
$work_end = '20:00';

// วันอาทิตย์เปิดสั้นกว่า
$day_of_week = date('w', strtotime($date));
if ($day_of_week == 0) { // วันอาทิตย์
    $work_start = '10:00';
    $work_end = '18:00';
}

// สร้างช่วงเวลาที่เป็นไปได้ (ทุก 30 นาที)
$time_slots = [];
$current_time = strtotime($work_start);
$end_time = strtotime($work_end);

while ($current_time < $end_time) {
    $slot_time = date('H:i:s', $current_time);
    $slot_end = $current_time + ($service['duration'] * 60);
    
    // ตรวจสอบว่าบริการจะเสร็จก่อนเวลาปิดหรือไม่
    if ($slot_end <= $end_time) {
        // ตรวจสอบว่าช่วงเวลานี้ว่างหรือไม่
        $is_available = isTimeSlotAvailable($barber_id, $date, $slot_time, $service['duration']);
        
        $time_slots[] = [
            'time' => $slot_time,
            'display' => formatTime12Hour($slot_time),
            'available' => $is_available
        ];
    }
    
    $current_time += 1800; // เพิ่ม 30 นาที
}

// สร้าง HTML สำหรับช่วงเวลา
$html = '';
if (empty($time_slots)) {
    $html = '<div class="alert alert-warning"><i class="fas fa-info-circle"></i> ไม่มีช่วงเวลาที่เหมาะสมในวันนี้</div>';
} else {
    $html = '<div class="row g-2">';
    foreach ($time_slots as $slot) {
        $class = $slot['available'] ? 'time-slot' : 'time-slot unavailable';
        $disabled = $slot['available'] ? '' : 'disabled';

        $html .= '<div class="col-md-3 col-sm-4 col-6">';
        $html .= '<div class="' . $class . '" data-time="' . $slot['time'] . '" ' . $disabled . '>';
        $html .= '<strong>' . $slot['display'] . '</strong>';
        if (!$slot['available']) {
            $html .= '<br><small class="text-muted">ไม่ว่าง</small>';
        }
        $html .= '</div>';
        $html .= '</div>';
    }
    $html .= '</div>';

    $available_count = count(array_filter($time_slots, function($slot) { return $slot['available']; }));
    if ($available_count == 0) {
        $html .= '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle"></i> ไม่มีช่วงเวลาว่างในวันนี้ กรุณาเลือกวันอื่น</div>';
    } else {
        $html .= '<div class="alert alert-info mt-3"><i class="fas fa-info-circle"></i> พบช่วงเวลาว่าง ' . $available_count . ' ช่วง กรุณาเลือกเวลาที่ต้องการ</div>';
    }
}

echo json_encode([
    'success' => true,
    'html' => $html,
    'available_slots' => count(array_filter($time_slots, function($slot) { return $slot['available']; }))
]);
?>
