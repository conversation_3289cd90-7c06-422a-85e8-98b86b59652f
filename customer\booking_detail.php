<?php
require_once __DIR__ . '/../includes/auth.php';
requireCustomer();

$booking_id = $_GET['id'] ?? '';
$success = $_GET['success'] ?? '';

if (empty($booking_id)) {
    header('Location: my_bookings.php');
    exit;
}

// ดึงข้อมูลการจอง
$booking = fetchOne("
    SELECT b.*, s.name as service_name, s.description as service_description, s.duration, s.price as service_price,
           br.name as barber_name, br.phone as barber_phone, br.speciality,
           u.full_name as customer_name, u.email as customer_email, u.phone as customer_phone
    FROM bookings b
    JOIN services s ON b.service_id = s.id
    JOIN barbers br ON b.barber_id = br.id
    JOIN users u ON b.user_id = u.id
    WHERE b.id = ? AND b.user_id = ?
", [$booking_id, $_SESSION['user_id']]);

if (!$booking) {
    header('Location: my_bookings.php');
    exit;
}

// คำนวณเวลาสิ้นสุด
$end_time = date('H:i:s', strtotime($booking['booking_time']) + ($booking['duration'] * 60));
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ใบเสร็จรับเงิน #<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?> - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">

    <!-- Print Styles -->
    <style>
        @media print {
            .no-print { display: none !important; }
            .navbar, .btn, .alert { display: none !important; }

            @page {
                size: 80mm auto;
                margin: 5mm;
            }

            body {
                font-family: 'Arial', sans-serif;
                font-size: 10px;
                line-height: 1.2;
                color: #000;
                background: white;
                margin: 0;
                padding: 0;
                width: 70mm;
            }

            .print-header {
                text-align: center;
                border-bottom: 1px solid #000;
                padding-bottom: 5px;
                margin-bottom: 8px;
            }

            .print-header h1 {
                font-size: 12px;
                font-weight: bold;
                margin: 0;
                color: #000;
            }

            .print-header .company-info {
                font-size: 8px;
                margin-top: 3px;
                color: #000;
                line-height: 1.1;
            }

            .booking-receipt {
                border: 1px solid #000;
                padding: 8px;
                margin: 0;
                background: white;
                width: 70mm;
                box-sizing: border-box;
            }

            .receipt-title {
                text-align: center;
                font-size: 10px;
                font-weight: bold;
                margin-bottom: 8px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .receipt-info {
                display: table;
                width: 100%;
                margin-bottom: 15px;
            }

            .receipt-row {
                display: table-row;
            }

            .receipt-label {
                display: table-cell;
                width: 35%;
                padding: 4px 0;
                font-weight: bold;
                border-bottom: 1px dotted #ccc;
                font-size: 12px;
            }

            .receipt-value {
                display: table-cell;
                padding: 4px 0 4px 15px;
                border-bottom: 1px dotted #ccc;
                font-size: 12px;
            }

            .total-section {
                border-top: 2px solid #000;
                margin-top: 20px;
                padding-top: 15px;
            }

            .total-amount {
                font-size: 18px;
                font-weight: bold;
                text-align: right;
            }

            .print-footer {
                margin-top: 40px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 20px;
            }

            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }

            .signature-box {
                width: 200px;
                text-align: center;
            }

            .signature-line {
                border-bottom: 1px solid #000;
                margin-bottom: 5px;
                height: 40px;
            }

            .qr-code {
                text-align: center;
                margin: 20px 0;
            }
        }

        @media screen {
            .print-only { display: none; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="book.php">จองคิว</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my_bookings.php">การจองของฉัน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../public/services.php">บริการ</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Success Message -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> 
                จองคิวสำเร็จ! เราจะติดต่อกลับเพื่อยืนยันการจองภายใน 24 ชั่วโมง
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Print Header (Only visible when printing and completed) -->
        <?php if ($booking['status'] === 'completed'): ?>
            <div class="print-only">
                <div class="print-header">
                    <h1>ร้านตัดผม BARBER SHOP</h1>
                    <div class="company-info">
                        123 ถนนสุขุมวิท กรุงเทพฯ<br>
                        โทร: ************<br>
                        เลขประจำตัวผู้เสียภาษี: 1234567890123
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4 no-print">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-receipt text-primary"></i>
                    ใบเสร็จรับเงิน #<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?>
                </h2>
                <p class="text-muted">ใบเสร็จการชำระเงินค่าบริการ</p>
            </div>
            <div class="col-md-4 text-end">
                <?php if ($booking['status'] === 'completed'): ?>
                    <div class="btn-group me-2" role="group">
                        <button onclick="printReceipt()" class="btn btn-success">
                            <i class="fas fa-print"></i> พิมพ์ใบเสร็จ 80mm
                        </button>
                        <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="printReceipt()">
                                <i class="fas fa-receipt"></i> พิมพ์แบบ 80mm (ใบเสร็จความร้อน)
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="printReceiptOptimized()">
                                <i class="fas fa-print"></i> พิมพ์แบบหน้าต่างใหม่
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="checkStatusAndPrint()">
                                <i class="fas fa-file-alt"></i> พิมพ์แบบปกติ (A4)
                            </a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info d-inline-block me-2 mb-0 py-2 px-3">
                        <i class="fas fa-info-circle"></i>
                        <small>ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น</small>
                    </div>
                <?php endif; ?>
                <a href="my_bookings.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> กลับ
                </a>
            </div>
        </div>

        <!-- Official Receipt (Print Version) -->
        <div class="print-only">
            <div class="booking-receipt">
                <div class="receipt-title">ใบเสร็จรับเงิน</div>
                <div style="text-align: center; margin-bottom: 8px; font-size: 8px;">
                    เลขที่: RCP-<?= str_pad($booking['id'], 4, '0', STR_PAD_LEFT) ?>
                </div>

                <div style="font-size: 8px; margin-bottom: 8px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>วันที่:</span>
                        <span><?= date('d/m/Y H:i') ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>ลูกค้า:</span>
                        <span><?= sanitize($booking['customer_name']) ?></span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>โทร:</span>
                        <span><?= sanitize($booking['customer_phone']) ?></span>
                    </div>
                </div>

                <div style="border-top: 1px dashed #000; border-bottom: 1px dashed #000; padding: 5px 0; margin: 8px 0;">
                    <div style="font-size: 8px; font-weight: bold; text-align: center; margin-bottom: 3px;">รายการ</div>

                    <div style="font-size: 8px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 2px;">
                            <span style="width: 60%;"><?= sanitize($booking['service_name']) ?></span>
                            <span style="width: 15%; text-align: center;">1</span>
                            <span style="width: 25%; text-align: right;"><?= number_format($booking['total_price'], 0) ?></span>
                        </div>
                        <div style="font-size: 7px; color: #666; margin-bottom: 3px;">
                            <?= formatDateThai($booking['booking_date']) ?><br>
                            เวลา: <?= formatTime12Hour($booking['booking_time']) ?>-<?= formatTime12Hour($end_time) ?><br>
                            ช่าง: <?= sanitize($booking['barber_name']) ?>
                            <?php if ($booking['notes']): ?>
                            <br>หมายเหตุ: <?= sanitize($booking['notes']) ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div style="border-top: 1px dashed #000; padding-top: 5px; margin-top: 5px;">
                    <div style="font-size: 8px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 2px;">
                            <span>รวม:</span>
                            <span><?= number_format($booking['total_price'], 0) ?> บาท</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 2px;">
                            <span>ส่วนลด:</span>
                            <span>0 บาท</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-weight: bold; border-top: 1px solid #000; padding-top: 2px;">
                            <span>รวมทั้งสิ้น:</span>
                            <span><?= number_format($booking['total_price'], 0) ?> บาท</span>
                        </div>
                    </div>

                    <div style="margin-top: 5px; font-size: 7px;">
                        <div><strong>จำนวนเงิน (ตัวอักษร):</strong></div>
                        <div><?php
                        $amount_text = convertNumberToThaiText($booking['total_price']);
                        echo $amount_text . "บาทถ้วน";
                        ?></div>
                    </div>

                    <div style="margin-top: 5px; font-size: 7px;">
                        <strong>ชำระโดย:</strong> ☐ เงินสด ☐ โอน ☐ บัตร
                    </div>
                </div>



                </div>

                <div style="margin-top: 8px; border-top: 1px dashed #000; padding-top: 5px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <div style="width: 45%; text-align: center;">
                            <div style="border-bottom: 1px solid #000; margin-bottom: 2px; height: 20px;"></div>
                            <div style="font-size: 7px;">ผู้รับเงิน</div>
                        </div>
                        <div style="width: 45%; text-align: center;">
                            <div style="border-bottom: 1px solid #000; margin-bottom: 2px; height: 20px;"></div>
                            <div style="font-size: 7px;">ผู้จ่ายเงิน</div>
                        </div>
                    </div>

                    <div style="text-align: center; font-size: 6px; margin-top: 5px;">
                        <div>*** ขอบคุณที่ใช้บริการ ***</div>
                        <div>กรุณาเก็บใบเสร็จไว้เป็นหลักฐาน</div>
                        <div style="margin-top: 3px;">
                            <?= date('d/m/Y H:i') ?> | #<?= str_pad($booking['id'], 4, '0', STR_PAD_LEFT) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>

        <div class="row no-print">
            <!-- Booking Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> ข้อมูลการจอง
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">บริการ</h6>
                                <p class="mb-3">
                                    <strong><?= sanitize($booking['service_name']) ?></strong><br>
                                    <small class="text-muted"><?= sanitize($booking['service_description']) ?></small>
                                </p>

                                <h6 class="text-primary">ช่างตัดผม</h6>
                                <p class="mb-3">
                                    <strong><?= sanitize($booking['barber_name']) ?></strong><br>
                                    <?php if ($booking['speciality']): ?>
                                        <small class="text-muted">ความเชี่ยวชาญ: <?= sanitize($booking['speciality']) ?></small><br>
                                    <?php endif; ?>
                                    <?php if ($booking['barber_phone']): ?>
                                        <small class="text-muted">โทร: <?= sanitize($booking['barber_phone']) ?></small>
                                    <?php endif; ?>
                                </p>

                                <h6 class="text-primary">วันที่และเวลา</h6>
                                <p class="mb-3">
                                    <i class="fas fa-calendar"></i> <?= formatDateThai($booking['booking_date']) ?><br>
                                    <i class="fas fa-clock"></i> <?= formatTime12Hour($booking['booking_time']) ?> - <?= formatTime12Hour($end_time) ?><br>
                                    <small class="text-muted">ระยะเวลา: <?= $booking['duration'] ?> นาที</small>
                                </p>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary">สถานะ</h6>
                                <p class="mb-3">
                                    <span class="badge status-<?= $booking['status'] ?> fs-6">
                                        <?php
                                        $status_text = [
                                            'pending' => 'รอยืนยัน',
                                            'confirmed' => 'ยืนยันแล้ว',
                                            'completed' => 'เสร็จสิ้น',
                                            'cancelled' => 'ยกเลิก'
                                        ];
                                        echo $status_text[$booking['status']];
                                        ?>
                                    </span>
                                    <?php if ($booking['status'] !== 'completed'): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น
                                        </small>
                                    <?php endif; ?>
                                </p>

                                <h6 class="text-primary">ราคา</h6>
                                <p class="mb-3">
                                    <span class="h4 text-success"><?= number_format($booking['total_price']) ?> บาท</span><br>
                                    <?php if ($booking['total_price'] < $booking['service_price']): ?>
                                        <small class="text-muted">
                                            ราคาปกติ: <?= number_format($booking['service_price']) ?> บาท
                                            (ประหยัด <?= number_format($booking['service_price'] - $booking['total_price']) ?> บาท)
                                        </small><br>
                                    <?php endif; ?>
                                    <?php if ($booking['status'] === 'completed'): ?>
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i>
                                            พร้อมพิมพ์ใบเสร็จ
                                        </small>
                                    <?php endif; ?>
                                </p>

                                <h6 class="text-primary">วันที่จอง</h6>
                                <p class="mb-3">
                                    <?= date('d/m/Y H:i:s', strtotime($booking['created_at'])) ?>
                                </p>

                                <?php if ($booking['notes']): ?>
                                    <h6 class="text-primary">หมายเหตุ</h6>
                                    <p class="mb-3">
                                        <?= nl2br(sanitize($booking['notes'])) ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs"></i> การดำเนินการ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <?php if ($booking['status'] === 'pending'): ?>
                                <button type="button" class="btn btn-danger" onclick="cancelBooking(<?= $booking['id'] ?>)">
                                    <i class="fas fa-times"></i> ยกเลิกการจอง
                                </button>
                            <?php endif; ?>

                            <?php if ($booking['status'] === 'completed'): ?>
                                <a href="book.php?service_id=<?= $booking['service_id'] ?>" class="btn btn-success">
                                    <i class="fas fa-redo"></i> จองซ้ำ
                                </a>
                            <?php endif; ?>

                            <?php if ($booking['status'] === 'completed'): ?>
                                <button type="button" class="btn btn-outline-primary" onclick="printReceipt()">
                                    <i class="fas fa-print"></i> พิมพ์ใบเสร็จ
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-outline-secondary" disabled title="ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น">
                                    <i class="fas fa-print"></i> พิมพ์ใบเสร็จ
                                </button>
                            <?php endif; ?>

                            <a href="my_bookings.php" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> ดูการจองทั้งหมด
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status Timeline -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-timeline"></i> สถานะการจอง
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item <?= in_array($booking['status'], ['pending', 'confirmed', 'completed']) ? 'active' : '' ?>">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6>ส่งคำขอจอง</h6>
                                    <small class="text-muted"><?= date('d/m/Y H:i', strtotime($booking['created_at'])) ?></small>
                                </div>
                            </div>

                            <div class="timeline-item <?= in_array($booking['status'], ['confirmed', 'completed']) ? 'active' : '' ?>">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6>ยืนยันการจอง</h6>
                                    <small class="text-muted">
                                        <?= $booking['status'] === 'confirmed' || $booking['status'] === 'completed' ? 'ยืนยันแล้ว' : 'รอยืนยัน' ?>
                                    </small>
                                </div>
                            </div>

                            <div class="timeline-item <?= $booking['status'] === 'completed' ? 'active' : '' ?>">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6>เสร็จสิ้นบริการ</h6>
                                    <small class="text-muted">
                                        <?= $booking['status'] === 'completed' ? 'เสร็จสิ้นแล้ว' : 'รอเสร็จสิ้น' ?>
                                    </small>
                                </div>
                            </div>

                            <?php if ($booking['status'] === 'cancelled'): ?>
                                <div class="timeline-item active">
                                    <div class="timeline-marker bg-danger"></div>
                                    <div class="timeline-content">
                                        <h6>ยกเลิกการจอง</h6>
                                        <small class="text-muted">ยกเลิกแล้ว</small>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-phone"></i> ติดต่อเรา
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><i class="fas fa-phone text-primary"></i> ************</p>
                        <p><i class="fas fa-envelope text-primary"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt text-primary"></i> 123 ถนนสุขุมวิท กรุงเทพฯ</p>
                        
                        <hr>
                        
                        <h6>เวลาทำการ</h6>
                        <p class="mb-1">จันทร์ - เสาร์: 09:00 - 20:00</p>
                        <p class="mb-0">อาทิตย์: 10:00 - 18:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Print Button -->
    <?php if ($booking['status'] === 'completed'): ?>
        <div class="btn-group-vertical print-btn no-print" role="group">
            <button onclick="printReceipt()" class="btn btn-success btn-sm">
                <i class="fas fa-print"></i> พิมพ์ 80mm
            </button>
            <button onclick="printReceiptOptimized()" class="btn btn-outline-success btn-sm">
                <i class="fas fa-external-link-alt"></i> หน้าต่างใหม่
            </button>
        </div>
    <?php endif; ?>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการยกเลิก</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณแน่ใจหรือไม่ที่จะยกเลิกการจองนี้?</p>
                    <p class="text-muted">การยกเลิกจะไม่สามารถย้อนกลับได้</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ไม่ยกเลิก</button>
                    <button type="button" class="btn btn-danger" id="confirmCancel">ยืนยันการยกเลิก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        function cancelBooking(bookingId) {
            const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
            modal.show();

            document.getElementById('confirmCancel').onclick = function() {
                fetch('../customer/ajax/cancel_booking.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `booking_id=${bookingId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการยกเลิกการจอง');
                });

                modal.hide();
            };
        }

        // Check status before printing
        function checkStatusAndPrint() {
            <?php if ($booking['status'] !== 'completed'): ?>
                alert('ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น');
                return;
            <?php endif; ?>
            window.print();
        }

        // Enhanced print function for 80mm receipt
        function printReceipt() {
            // ตรวจสอบสถานะก่อนพิมพ์
            <?php if ($booking['status'] !== 'completed'): ?>
                alert('ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น');
                return;
            <?php endif; ?>

            // Hide all non-print elements
            document.body.classList.add('printing');

            // Set page title for print
            const originalTitle = document.title;
            document.title = `ใบเสร็จรับเงิน #<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?> - ร้านตัดผม Barber Shop`;

            // Create print-specific styles for 80mm
            const printStyle = document.createElement('style');
            printStyle.id = 'print-80mm-style';
            printStyle.innerHTML = `
                @media print {
                    @page {
                        size: 80mm auto;
                        margin: 3mm !important;
                    }
                    body {
                        width: 74mm !important;
                        font-size: 9px !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        font-family: 'Courier New', monospace !important;
                    }
                    .booking-receipt {
                        width: 74mm !important;
                        border: none !important;
                        padding: 3px !important;
                        margin: 0 !important;
                    }
                }
            `;
            document.head.appendChild(printStyle);

            // Print with delay to ensure styles are applied
            setTimeout(() => {
                window.print();

                // Clean up after printing
                setTimeout(() => {
                    if (document.getElementById('print-80mm-style')) {
                        document.head.removeChild(printStyle);
                    }
                    document.title = originalTitle;
                    document.body.classList.remove('printing');
                }, 1000);
            }, 100);
        }

        // Alternative optimized print function
        function printReceiptOptimized() {
            // ตรวจสอบสถานะก่อนพิมพ์
            <?php if ($booking['status'] !== 'completed'): ?>
                alert('ใบเสร็จจะพร้อมใช้งานเมื่อบริการเสร็จสิ้น');
                return;
            <?php endif; ?>

            // Create a new window for printing
            const printWindow = window.open('', '_blank', 'width=300,height=600,scrollbars=no,resizable=no');

            // Get the receipt content
            const receiptContent = document.querySelector('.print-only').innerHTML;

            // Create optimized HTML for 80mm printing
            const printHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>ใบเสร็จรับเงิน #<?= str_pad($booking['id'], 6, '0', STR_PAD_LEFT) ?></title>
                    <style>
                        @page {
                            size: 80mm auto;
                            margin: 2mm;
                        }
                        body {
                            font-family: 'Courier New', monospace;
                            font-size: 9px;
                            line-height: 1.1;
                            margin: 0;
                            padding: 0;
                            width: 76mm;
                            color: #000;
                            background: white;
                        }
                        .booking-receipt {
                            width: 100%;
                            border: none;
                            padding: 3px;
                            margin: 0;
                            background: white;
                        }
                        .print-header {
                            text-align: center;
                            border-bottom: 1px solid #000;
                            padding-bottom: 3px;
                            margin-bottom: 5px;
                        }
                        .print-header h1 {
                            font-size: 11px;
                            font-weight: bold;
                            margin: 0;
                        }
                        .print-header .company-info {
                            font-size: 7px;
                            margin-top: 2px;
                            line-height: 1.0;
                        }
                        .receipt-title {
                            text-align: center;
                            font-size: 9px;
                            font-weight: bold;
                            margin-bottom: 5px;
                            text-transform: uppercase;
                        }
                    </style>
                </head>
                <body onload="window.print(); setTimeout(function(){ window.close(); }, 1000);">
                    ${receiptContent}
                </body>
                </html>
            `;

            printWindow.document.write(printHTML);
            printWindow.document.close();
        }

        // Add print event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Add keyboard shortcut Ctrl+P
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    printReceipt();
                }
            });

            // Update all print buttons
            const printButtons = document.querySelectorAll('[onclick*="window.print"]');
            printButtons.forEach(button => {
                button.onclick = function(e) {
                    e.preventDefault();
                    printReceipt();
                };
            });
        });

        // Print-specific styles
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });

        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    </script>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -23px;
            top: 5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }

        .timeline-item.active .timeline-marker {
            box-shadow: 0 0 0 2px var(--bs-primary);
        }

        /* Additional print styles */
        @media print {
            .navbar, .card-header, .btn, .modal, .timeline { display: none !important; }
            .card { border: none !important; box-shadow: none !important; }
            .no-print { display: none !important; }
        }

        /* Screen-only styles */
        @media screen {
            .print-only { display: none !important; }
        }

        /* Print button styling */
        .print-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            border-radius: 50px;
            padding: 15px 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        @media print {
            .print-btn { display: none !important; }
        }
    </style>
</body>
</html>
