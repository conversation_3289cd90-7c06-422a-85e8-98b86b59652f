<?php
require_once __DIR__ . '/../includes/auth.php';
requireAdmin();

$success_message = '';
$error_message = '';

// ดึงข้อมูลผู้ใช้ทั้งหมด (ยกเว้นแอดมิน)
$users = fetchAll("
    SELECT u.*, 
           COUNT(b.id) as total_bookings,
           COALESCE(SUM(CASE WHEN b.status = 'completed' THEN b.total_price ELSE 0 END), 0) as total_spent
    FROM users u
    LEFT JOIN bookings b ON u.id = b.user_id
    WHERE u.role = 'customer'
    GROUP BY u.id
    ORDER BY u.created_at DESC
");

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_user') {
        // เพิ่มผู้ใช้ใหม่
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $full_name = trim($_POST['full_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $role = $_POST['role'] ?? 'customer';

        if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
            $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
        } elseif (strlen($password) < 6) {
            $error_message = 'รหัสผ่านต้องมีความยาวอย่างน้อย 6 ตัวอักษร';
        } elseif (!isValidEmail($email)) {
            $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
        } else {
            // ตรวจสอบว่ามีผู้ใช้นี้แล้วหรือไม่
            $existing = fetchOne("SELECT id FROM users WHERE username = ? OR email = ?", [$username, $email]);

            if ($existing) {
                $error_message = 'ชื่อผู้ใช้หรืออีเมลนี้มีอยู่แล้ว';
            } else {
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                $sql = "INSERT INTO users (username, email, password, full_name, phone, role, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
                $result = executeQuery($sql, [$username, $email, $hashed_password, $full_name, $phone, $role]);

                if ($result) {
                    $success_message = 'เพิ่มผู้ใช้สำเร็จ';
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการเพิ่มผู้ใช้';
                }
            }
        }
    } elseif ($action === 'edit_user') {
        // แก้ไขผู้ใช้
        $user_id = intval($_POST['user_id']);
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $full_name = trim($_POST['full_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $role = $_POST['role'] ?? 'customer';
        $new_password = $_POST['new_password'] ?? '';

        if (empty($username) || empty($email) || empty($full_name)) {
            $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
        } elseif (!isValidEmail($email)) {
            $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
        } else {
            // ตรวจสอบว่ามีผู้ใช้อื่นใช้ username หรือ email นี้หรือไม่
            $existing = fetchOne("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?", [$username, $email, $user_id]);

            if ($existing) {
                $error_message = 'ชื่อผู้ใช้หรืออีเมลนี้มีผู้อื่นใช้แล้ว';
            } else {
                if (!empty($new_password)) {
                    if (strlen($new_password) < 6) {
                        $error_message = 'รหัสผ่านใหม่ต้องมีความยาวอย่างน้อย 6 ตัวอักษร';
                    } else {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $sql = "UPDATE users SET username = ?, email = ?, password = ?, full_name = ?, phone = ?, role = ?, updated_at = NOW() WHERE id = ?";
                        $result = executeQuery($sql, [$username, $email, $hashed_password, $full_name, $phone, $role, $user_id]);
                    }
                } else {
                    $sql = "UPDATE users SET username = ?, email = ?, full_name = ?, phone = ?, role = ?, updated_at = NOW() WHERE id = ?";
                    $result = executeQuery($sql, [$username, $email, $full_name, $phone, $role, $user_id]);
                }

                if (isset($result) && $result) {
                    $success_message = 'แก้ไขข้อมูลผู้ใช้สำเร็จ';
                } else {
                    $error_message = 'เกิดข้อผิดพลาดในการแก้ไขข้อมูล';
                }
            }
        }
    } elseif ($action === 'delete_user') {
        $user_id = $_POST['user_id'] ?? '';

        if ($user_id) {
            // ตรวจสอบว่าไม่ใช่ผู้ใช้ปัจจุบัน
            if ($user_id == $_SESSION['user_id']) {
                $error_message = 'ไม่สามารถลบบัญชีของตัวเองได้';
            } else {
                // ตรวจสอบว่ามีการจองที่ยังไม่เสร็จสิ้นหรือไม่
                $active_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status IN ('pending', 'confirmed')", [$user_id])['count'];

                if ($active_bookings > 0) {
                    $error_message = 'ไม่สามารถลบผู้ใช้ได้ เนื่องจากมีการจองที่ยังไม่เสร็จสิ้น';
                } else {
                    // ลบผู้ใช้
                    $result = executeQuery("DELETE FROM users WHERE id = ? AND role = 'customer'", [$user_id]);

                    if ($result) {
                        $success_message = 'ลบผู้ใช้เรียบร้อยแล้ว';
                    } else {
                        $error_message = 'เกิดข้อผิดพลาดในการลบผู้ใช้';
                    }
                }
            }
        }
    }
}

if (isset($_GET['success'])) {
    $success_message = 'ดำเนินการเรียบร้อยแล้ว';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการผู้ใช้งาน - ร้านตัดผม Barber Shop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../public/index.php">
                <i class="fas fa-cut"></i> Barber Shop Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_bookings.php">จัดการการจอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_users.php">จัดการผู้ใช้</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_services.php">จัดการบริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_barbers.php">จัดการช่างตัดผม</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">รายงาน</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield"></i> <?= sanitize($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../public/index.php">ดูหน้าเว็บ</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../public/logout.php">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-users text-primary"></i> จัดการผู้ใช้งาน
                </h2>
                <p class="text-muted">จัดการข้อมูลลูกค้าและผู้ใช้งานในระบบ</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-success" onclick="exportUsers()">
                    <i class="fas fa-download"></i> ส่งออกข้อมูล
                </button>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?= sanitize($success_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle"></i> <?= sanitize($error_message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="dashboard-card stat-card bg-primary text-white">
                    <div class="stat-number"><?= count($users) ?></div>
                    <div class="stat-label">ลูกค้าทั้งหมด</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card stat-card bg-success text-white">
                    <div class="stat-number">
                        <?= count(array_filter($users, function($u) { return $u['total_bookings'] > 0; })) ?>
                    </div>
                    <div class="stat-label">ลูกค้าที่เคยใช้บริการ</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card stat-card bg-info text-white">
                    <div class="stat-number">
                        <?= count(array_filter($users, function($u) { return strtotime($u['created_at']) > strtotime('-30 days'); })) ?>
                    </div>
                    <div class="stat-label">สมาชิกใหม่ (30 วัน)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card stat-card bg-warning text-white">
                    <div class="stat-number">
                        <?= number_format(array_sum(array_column($users, 'total_spent'))) ?>
                    </div>
                    <div class="stat-label">รายได้รวม (บาท)</div>
                </div>
            </div>
        </div>

        <!-- Search -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <input type="text" class="form-control" id="search-users" placeholder="ค้นหาผู้ใช้งาน...">
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x text-muted mb-4"></i>
                                <h4>ยังไม่มีผู้ใช้งาน</h4>
                                <p class="text-muted">รอลูกค้าสมัครสมาชิกเข้ามาใช้งาน</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="users-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>ข้อมูลผู้ใช้</th>
                                            <th>ติดต่อ</th>
                                            <th>วันที่สมัคร</th>
                                            <th>การจอง</th>
                                            <th>ยอดใช้จ่าย</th>
                                            <th>การดำเนินการ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>#<?= $user['id'] ?></td>
                                            <td>
                                                <div>
                                                    <strong><?= sanitize($user['full_name']) ?></strong><br>
                                                    <small class="text-muted">@<?= sanitize($user['username']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="fas fa-envelope"></i> <?= sanitize($user['email']) ?><br>
                                                    <?php if ($user['phone']): ?>
                                                        <i class="fas fa-phone"></i> <?= sanitize($user['phone']) ?>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?= date('d/m/Y', strtotime($user['created_at'])) ?><br>
                                                <small class="text-muted">
                                                    <?php
                                                    $days_ago = floor((time() - strtotime($user['created_at'])) / 86400);
                                                    echo $days_ago == 0 ? 'วันนี้' : "$days_ago วันที่แล้ว";
                                                    ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= $user['total_bookings'] ?> ครั้ง</span>
                                                <?php if ($user['total_bookings'] > 0): ?>
                                                    <br><small class="text-muted">
                                                        <a href="manage_bookings.php?customer_id=<?= $user['id'] ?>">ดูการจอง</a>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong class="text-success"><?= number_format($user['total_spent']) ?> บาท</strong>
                                                <?php if ($user['total_spent'] > 0): ?>
                                                    <br><small class="text-muted">
                                                        เฉลี่ย <?= number_format($user['total_spent'] / max(1, $user['total_bookings'])) ?> บาท/ครั้ง
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" 
                                                            onclick="showUserDetails(<?= $user['id'] ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <button class="btn btn-outline-primary" 
                                                            onclick="sendEmail('<?= sanitize($user['email']) ?>')">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                    
                                                    <?php
                                                    // ตรวจสอบว่ามีการจองที่ยังไม่เสร็จสิ้นหรือไม่
                                                    $active_bookings = fetchOne("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND status IN ('pending', 'confirmed')", [$user['id']])['count'];
                                                    ?>
                                                    
                                                    <?php if ($active_bookings == 0): ?>
                                                        <button class="btn btn-outline-danger" 
                                                                onclick="deleteUser(<?= $user['id'] ?>, '<?= sanitize($user['full_name']) ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-secondary" disabled 
                                                                title="มีการจองที่ยังไม่เสร็จสิ้น">
                                                            <i class="fas fa-lock"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดผู้ใช้</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>คุณแน่ใจหรือไม่ที่จะลบผู้ใช้ <strong id="delete-user-name"></strong>?</p>
                    <p class="text-danger">การลบจะไม่สามารถย้อนกลับได้ และจะลบข้อมูลการจองทั้งหมดของผู้ใช้นี้</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">ลบผู้ใช้</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/script.js"></script>
    
    <script>
        let userToDelete = null;
        
        function showUserDetails(userId) {
            fetch(`ajax/get_user_details.php?id=${userId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const modal = document.getElementById('userModal');
                    const modalBody = modal.querySelector('.modal-body');
                    modalBody.innerHTML = data.html;
                    
                    const bootstrapModal = new bootstrap.Modal(modal);
                    bootstrapModal.show();
                } else {
                    alert('ไม่สามารถโหลดข้อมูลได้');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
            });
        }
        
        function deleteUser(userId, userName) {
            userToDelete = userId;
            document.getElementById('delete-user-name').textContent = userName;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        function sendEmail(email) {
            window.location.href = `mailto:${email}`;
        }
        
        function exportUsers() {
            window.open('export_users.php', '_blank');
        }
        
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (userToDelete) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" value="${userToDelete}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    </script>
</body>
</html>
